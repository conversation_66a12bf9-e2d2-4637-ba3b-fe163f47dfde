{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\pages\\\\homePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState } from \"react\";\nimport { Row, Col, Container, Card, Button, Badge } from \"react-bootstrap\";\nimport { Link } from \"react-router-dom\";\nimport ProductsContext from \"../context/productsContext\";\nimport Loader from \"../components/loader\";\nimport Message from \"../components/message\";\nimport AdminRedirect from '../components/AdminRedirect';\nimport \"../styles/homePage.css\";\nimport AISearch from '../components/AISearch';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction HomePage() {\n  _s();\n  const {\n    products,\n    loading,\n    error,\n    loadProducts,\n    productsLoaded,\n    brands,\n    categories\n  } = useContext(ProductsContext);\n\n  // <PERSON> chuyển useState lên đầu component\n  const [showAISearch, setShowAISearch] = useState(false);\n  useEffect(() => {\n    if (!productsLoaded) loadProducts();\n    window.scrollTo(0, 0);\n  }, [productsLoaded, loadProducts]);\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(Message, {\n    variant: \"danger\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 21\n  }, this);\n\n  // Sản phẩm bán chạy\n  const bestSellingProducts = [...products].sort((a, b) => (b.total_sold || 0) - (a.total_sold || 0)).slice(0, 8);\n\n  // Sản phẩm được đánh giá cao\n  const topRatedProducts = [...products].sort((a, b) => Number(b.rating || 0) - Number(a.rating || 0)).slice(0, 8);\n\n  // Sản phẩm mới\n  const newProducts = [...products].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, 8);\n\n  // Format giá tiền\n  const formatPrice = price => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND'\n    }).format(price);\n  };\n  return /*#__PURE__*/_jsxDEV(AdminRedirect, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-banner hero-banner\",\n        style: {\n          backgroundImage: `url(${process.env.PUBLIC_URL}/images/Rectangle2.png)`,\n          backgroundSize: 'cover',\n          backgroundPosition: 'center',\n          backgroundRepeat: 'no-repeat',\n          color: '#fff',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: \"Kh\\xE1m ph\\xE1 th\\u1EBF gi\\u1EDBi c\\xF4ng ngh\\u1EC7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle\",\n            children: \"S\\u1EA3n ph\\u1EA9m ch\\u1EA5t l\\u01B0\\u1EE3ng cao v\\u1EDBi gi\\xE1 t\\u1ED1t nh\\u1EA5t\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-buttons mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/search\",\n              className: \"btn btn-light rounded-pill px-4 py-2 me-3\",\n              children: \"SHOP NOW\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-light\",\n              className: \"rounded-pill px-4 py-2\",\n              onClick: () => setShowAISearch(true),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-camera me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), \"AI SEARCH\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        className: \"py-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"S\\u1EA3n ph\\u1EA9m b\\xE1n ch\\u1EA1y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: bestSellingProducts.map(product => /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 4,\n              lg: 3,\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"product-card h-100\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: `/products/${product.id}`,\n                  children: /*#__PURE__*/_jsxDEV(Card.Img, {\n                    variant: \"top\",\n                    src: product.image,\n                    className: \"product-image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n                    className: \"product-title\",\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/products/${product.id}`,\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 98,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-price\",\n                    children: formatPrice(product.price)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-rating\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-star\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 106,\n                      columnNumber: 25\n                    }, this), product.rating || 0, \" (\", product.numReviews || 0, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this)\n            }, product.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"S\\u1EA3n ph\\u1EA9m \\u0111\\u01B0\\u1EE3c \\u0111\\xE1nh gi\\xE1 cao\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: topRatedProducts.map(product => /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 4,\n              lg: 3,\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"product-card h-100\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: `/products/${product.id}`,\n                  children: /*#__PURE__*/_jsxDEV(Card.Img, {\n                    variant: \"top\",\n                    src: product.image,\n                    className: \"product-image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n                    className: \"product-title\",\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/products/${product.id}`,\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-price\",\n                    children: formatPrice(product.price)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-rating\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-star\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 25\n                    }, this), product.rating || 0, \" (\", product.numReviews || 0, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this)\n            }, product.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"S\\u1EA3n ph\\u1EA9m m\\u1EDBi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: newProducts.map(product => /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 4,\n              lg: 3,\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"product-card h-100\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: `/products/${product.id}`,\n                  children: /*#__PURE__*/_jsxDEV(Card.Img, {\n                    variant: \"top\",\n                    src: product.image,\n                    className: \"product-image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n                    className: \"product-title\",\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/products/${product.id}`,\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-price\",\n                    children: formatPrice(product.price)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-rating\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-star\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 25\n                    }, this), product.rating || 0, \" (\", product.numReviews || 0, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)\n            }, product.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AISearch, {\n        show: showAISearch,\n        onHide: () => setShowAISearch(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(HomePage, \"boXRxDbzdSWPXMU9Xbkxg5pn+lc=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "Row", "Col", "Container", "Card", "<PERSON><PERSON>", "Badge", "Link", "ProductsContext", "Loader", "Message", "AdminRedirect", "AISearch", "jsxDEV", "_jsxDEV", "HomePage", "_s", "products", "loading", "error", "loadProducts", "productsLoaded", "brands", "categories", "showAISearch", "setShowAISearch", "window", "scrollTo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "children", "bestSellingProducts", "sort", "a", "b", "total_sold", "slice", "topRatedProducts", "Number", "rating", "newProducts", "Date", "createdAt", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "format", "className", "backgroundImage", "process", "env", "PUBLIC_URL", "backgroundSize", "backgroundPosition", "backgroundRepeat", "color", "position", "to", "onClick", "map", "product", "sm", "md", "lg", "id", "Img", "src", "image", "Body", "Title", "name", "numReviews", "show", "onHide", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/pages/homePage.jsx"], "sourcesContent": ["import React, { useContext, useEffect, useState } from \"react\";\nimport { <PERSON>, Col, Container, Card, Button, Badge } from \"react-bootstrap\";\nimport { Link } from \"react-router-dom\";\nimport ProductsContext from \"../context/productsContext\";\nimport Loader from \"../components/loader\";\nimport Message from \"../components/message\";\nimport AdminRedirect from '../components/AdminRedirect';\nimport \"../styles/homePage.css\";\nimport AISearch from '../components/AISearch';\n\nfunction HomePage() {\n  const { products, loading, error, loadProducts, productsLoaded, brands, categories } = useContext(ProductsContext);\n  \n  // Di chuyển useState lên đầu component\n  const [showAISearch, setShowAISearch] = useState(false);\n  \n  useEffect(() => {\n    if (!productsLoaded) loadProducts();\n    window.scrollTo(0, 0);\n  }, [productsLoaded, loadProducts]);\n\n  if (loading) return <Loader />;\n  if (error) return <Message variant=\"danger\">{error}</Message>;\n\n  // Sản phẩm bán chạy\n  const bestSellingProducts = [...products]\n    .sort((a, b) => (b.total_sold || 0) - (a.total_sold || 0))\n    .slice(0, 8);\n\n  // Sản phẩm được đánh giá cao\n  const topRatedProducts = [...products]\n    .sort((a, b) => Number(b.rating || 0) - Number(a.rating || 0))\n    .slice(0, 8);\n\n  // Sản phẩm mới\n  const newProducts = [...products]\n    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))\n    .slice(0, 8);\n\n  // Format giá tiền\n  const formatPrice = (price) => {\n    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);\n  };\n\n  return (\n    <AdminRedirect>\n      <div className=\"home-page\">\n        {/* Main Banner */}\n        <div\n          className=\"main-banner hero-banner\"\n          style={{\n            backgroundImage: `url(${process.env.PUBLIC_URL}/images/Rectangle2.png)`,\n            backgroundSize: 'cover',\n            backgroundPosition: 'center',\n            backgroundRepeat: 'no-repeat',\n            color: '#fff',\n            position: 'relative',\n          }}\n        >\n          {/* Hero content */}\n          <div className=\"hero-content text-center\">\n            <h1 className=\"hero-title\">Khám phá thế giới công nghệ</h1>\n            <p className=\"hero-subtitle\">Sản phẩm chất lượng cao với giá tốt nhất</p>\n            <div className=\"hero-buttons mt-4\">\n              <Link to=\"/search\" className=\"btn btn-light rounded-pill px-4 py-2 me-3\">\n                SHOP NOW\n              </Link>\n              <Button \n                variant=\"outline-light\" \n                className=\"rounded-pill px-4 py-2\"\n                onClick={() => setShowAISearch(true)}\n              >\n                <i className=\"fas fa-camera me-2\"></i>\n                AI SEARCH\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Rest of your component content */}\n        <Container className=\"py-5\">\n          {/* Sản phẩm bán chạy */}\n          <section className=\"mb-5\">\n            <h2 className=\"section-title\">Sản phẩm bán chạy</h2>\n            <Row>\n              {bestSellingProducts.map(product => (\n                <Col key={product.id} sm={6} md={4} lg={3} className=\"mb-4\">\n                  <Card className=\"product-card h-100\">\n                    <Link to={`/products/${product.id}`}>\n                      <Card.Img \n                        variant=\"top\" \n                        src={product.image} \n                        className=\"product-image\"\n                      />\n                    </Link>\n                    <Card.Body>\n                      <Card.Title className=\"product-title\">\n                        <Link to={`/products/${product.id}`}>\n                          {product.name}\n                        </Link>\n                      </Card.Title>\n                      <div className=\"product-price\">\n                        {formatPrice(product.price)}\n                      </div>\n                      <div className=\"product-rating\">\n                        <i className=\"fas fa-star\"></i>\n                        {product.rating || 0} ({product.numReviews || 0})\n                      </div>\n                    </Card.Body>\n                  </Card>\n                </Col>\n              ))}\n            </Row>\n          </section>\n\n          {/* Sản phẩm đánh giá cao */}\n          <section className=\"mb-5\">\n            <h2 className=\"section-title\">Sản phẩm được đánh giá cao</h2>\n            <Row>\n              {topRatedProducts.map(product => (\n                <Col key={product.id} sm={6} md={4} lg={3} className=\"mb-4\">\n                  <Card className=\"product-card h-100\">\n                    <Link to={`/products/${product.id}`}>\n                      <Card.Img \n                        variant=\"top\" \n                        src={product.image} \n                        className=\"product-image\"\n                      />\n                    </Link>\n                    <Card.Body>\n                      <Card.Title className=\"product-title\">\n                        <Link to={`/products/${product.id}`}>\n                          {product.name}\n                        </Link>\n                      </Card.Title>\n                      <div className=\"product-price\">\n                        {formatPrice(product.price)}\n                      </div>\n                      <div className=\"product-rating\">\n                        <i className=\"fas fa-star\"></i>\n                        {product.rating || 0} ({product.numReviews || 0})\n                      </div>\n                    </Card.Body>\n                  </Card>\n                </Col>\n              ))}\n            </Row>\n          </section>\n\n          {/* Sản phẩm mới */}\n          <section className=\"mb-5\">\n            <h2 className=\"section-title\">Sản phẩm mới</h2>\n            <Row>\n              {newProducts.map(product => (\n                <Col key={product.id} sm={6} md={4} lg={3} className=\"mb-4\">\n                  <Card className=\"product-card h-100\">\n                    <Link to={`/products/${product.id}`}>\n                      <Card.Img \n                        variant=\"top\" \n                        src={product.image} \n                        className=\"product-image\"\n                      />\n                    </Link>\n                    <Card.Body>\n                      <Card.Title className=\"product-title\">\n                        <Link to={`/products/${product.id}`}>\n                          {product.name}\n                        </Link>\n                      </Card.Title>\n                      <div className=\"product-price\">\n                        {formatPrice(product.price)}\n                      </div>\n                      <div className=\"product-rating\">\n                        <i className=\"fas fa-star\"></i>\n                        {product.rating || 0} ({product.numReviews || 0})\n                      </div>\n                    </Card.Body>\n                  </Card>\n                </Col>\n              ))}\n            </Row>\n          </section>\n        </Container>\n\n        {/* AI Search Modal */}\n        <AISearch \n          show={showAISearch} \n          onHide={() => setShowAISearch(false)} \n        />\n      </div>\n    </AdminRedirect>\n  );\n}\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,SAASC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,QAAQ,iBAAiB;AAC1E,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAO,wBAAwB;AAC/B,OAAOC,QAAQ,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,KAAK;IAAEC,YAAY;IAAEC,cAAc;IAAEC,MAAM;IAAEC;EAAW,CAAC,GAAGzB,UAAU,CAACU,eAAe,CAAC;;EAElH;EACA,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACd,IAAI,CAACsB,cAAc,EAAED,YAAY,EAAE;IACnCM,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,CAAC,EAAE,CAACN,cAAc,EAAED,YAAY,CAAC,CAAC;EAElC,IAAIF,OAAO,EAAE,oBAAOJ,OAAA,CAACL,MAAM;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAG;EAC9B,IAAIZ,KAAK,EAAE,oBAAOL,OAAA,CAACJ,OAAO;IAACsB,OAAO,EAAC,QAAQ;IAAAC,QAAA,EAAEd;EAAK;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAW;;EAE7D;EACA,MAAMG,mBAAmB,GAAG,CAAC,GAAGjB,QAAQ,CAAC,CACtCkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,UAAU,IAAI,CAAC,KAAKF,CAAC,CAACE,UAAU,IAAI,CAAC,CAAC,CAAC,CACzDC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEd;EACA,MAAMC,gBAAgB,GAAG,CAAC,GAAGvB,QAAQ,CAAC,CACnCkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKI,MAAM,CAACJ,CAAC,CAACK,MAAM,IAAI,CAAC,CAAC,GAAGD,MAAM,CAACL,CAAC,CAACM,MAAM,IAAI,CAAC,CAAC,CAAC,CAC7DH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEd;EACA,MAAMI,WAAW,GAAG,CAAC,GAAG1B,QAAQ,CAAC,CAC9BkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIO,IAAI,CAACP,CAAC,CAACQ,SAAS,CAAC,GAAG,IAAID,IAAI,CAACR,CAAC,CAACS,SAAS,CAAC,CAAC,CAC7DN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEd;EACA,MAAMO,WAAW,GAAIC,KAAK,IAAK;IAC7B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAAEC,KAAK,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAM,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAC7F,CAAC;EAED,oBACEjC,OAAA,CAACH,aAAa;IAAAsB,QAAA,eACZnB,OAAA;MAAKuC,SAAS,EAAC,WAAW;MAAApB,QAAA,gBAExBnB,OAAA;QACEuC,SAAS,EAAC,yBAAyB;QACnCH,KAAK,EAAE;UACLI,eAAe,EAAG,OAAMC,OAAO,CAACC,GAAG,CAACC,UAAW,yBAAwB;UACvEC,cAAc,EAAE,OAAO;UACvBC,kBAAkB,EAAE,QAAQ;UAC5BC,gBAAgB,EAAE,WAAW;UAC7BC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE;QACZ,CAAE;QAAA7B,QAAA,eAGFnB,OAAA;UAAKuC,SAAS,EAAC,0BAA0B;UAAApB,QAAA,gBACvCnB,OAAA;YAAIuC,SAAS,EAAC,YAAY;YAAApB,QAAA,EAAC;UAA2B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAC3DjB,OAAA;YAAGuC,SAAS,EAAC,eAAe;YAAApB,QAAA,EAAC;UAAwC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAI,eACzEjB,OAAA;YAAKuC,SAAS,EAAC,mBAAmB;YAAApB,QAAA,gBAChCnB,OAAA,CAACP,IAAI;cAACwD,EAAE,EAAC,SAAS;cAACV,SAAS,EAAC,2CAA2C;cAAApB,QAAA,EAAC;YAEzE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAO,eACPjB,OAAA,CAACT,MAAM;cACL2B,OAAO,EAAC,eAAe;cACvBqB,SAAS,EAAC,wBAAwB;cAClCW,OAAO,EAAEA,CAAA,KAAMvC,eAAe,CAAC,IAAI,CAAE;cAAAQ,QAAA,gBAErCnB,OAAA;gBAAGuC,SAAS,EAAC;cAAoB;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,aAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF,eAGNjB,OAAA,CAACX,SAAS;QAACkD,SAAS,EAAC,MAAM;QAAApB,QAAA,gBAEzBnB,OAAA;UAASuC,SAAS,EAAC,MAAM;UAAApB,QAAA,gBACvBnB,OAAA;YAAIuC,SAAS,EAAC,eAAe;YAAApB,QAAA,EAAC;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eACpDjB,OAAA,CAACb,GAAG;YAAAgC,QAAA,EACDC,mBAAmB,CAAC+B,GAAG,CAACC,OAAO,iBAC9BpD,OAAA,CAACZ,GAAG;cAAkBiE,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAChB,SAAS,EAAC,MAAM;cAAApB,QAAA,eACzDnB,OAAA,CAACV,IAAI;gBAACiD,SAAS,EAAC,oBAAoB;gBAAApB,QAAA,gBAClCnB,OAAA,CAACP,IAAI;kBAACwD,EAAE,EAAG,aAAYG,OAAO,CAACI,EAAG,EAAE;kBAAArC,QAAA,eAClCnB,OAAA,CAACV,IAAI,CAACmE,GAAG;oBACPvC,OAAO,EAAC,KAAK;oBACbwC,GAAG,EAAEN,OAAO,CAACO,KAAM;oBACnBpB,SAAS,EAAC;kBAAe;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACzB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPjB,OAAA,CAACV,IAAI,CAACsE,IAAI;kBAAAzC,QAAA,gBACRnB,OAAA,CAACV,IAAI,CAACuE,KAAK;oBAACtB,SAAS,EAAC,eAAe;oBAAApB,QAAA,eACnCnB,OAAA,CAACP,IAAI;sBAACwD,EAAE,EAAG,aAAYG,OAAO,CAACI,EAAG,EAAE;sBAAArC,QAAA,EACjCiC,OAAO,CAACU;oBAAI;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBACR;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACI,eACbjB,OAAA;oBAAKuC,SAAS,EAAC,eAAe;oBAAApB,QAAA,EAC3Ba,WAAW,CAACoB,OAAO,CAACnB,KAAK;kBAAC;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACvB,eACNjB,OAAA;oBAAKuC,SAAS,EAAC,gBAAgB;oBAAApB,QAAA,gBAC7BnB,OAAA;sBAAGuC,SAAS,EAAC;oBAAa;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAK,EAC9BmC,OAAO,CAACxB,MAAM,IAAI,CAAC,EAAC,IAAE,EAACwB,OAAO,CAACW,UAAU,IAAI,CAAC,EAAC,GAClD;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAM;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP,GAvBCmC,OAAO,CAACI,EAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAyBrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACE,eAGVjB,OAAA;UAASuC,SAAS,EAAC,MAAM;UAAApB,QAAA,gBACvBnB,OAAA;YAAIuC,SAAS,EAAC,eAAe;YAAApB,QAAA,EAAC;UAA0B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAC7DjB,OAAA,CAACb,GAAG;YAAAgC,QAAA,EACDO,gBAAgB,CAACyB,GAAG,CAACC,OAAO,iBAC3BpD,OAAA,CAACZ,GAAG;cAAkBiE,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAChB,SAAS,EAAC,MAAM;cAAApB,QAAA,eACzDnB,OAAA,CAACV,IAAI;gBAACiD,SAAS,EAAC,oBAAoB;gBAAApB,QAAA,gBAClCnB,OAAA,CAACP,IAAI;kBAACwD,EAAE,EAAG,aAAYG,OAAO,CAACI,EAAG,EAAE;kBAAArC,QAAA,eAClCnB,OAAA,CAACV,IAAI,CAACmE,GAAG;oBACPvC,OAAO,EAAC,KAAK;oBACbwC,GAAG,EAAEN,OAAO,CAACO,KAAM;oBACnBpB,SAAS,EAAC;kBAAe;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACzB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPjB,OAAA,CAACV,IAAI,CAACsE,IAAI;kBAAAzC,QAAA,gBACRnB,OAAA,CAACV,IAAI,CAACuE,KAAK;oBAACtB,SAAS,EAAC,eAAe;oBAAApB,QAAA,eACnCnB,OAAA,CAACP,IAAI;sBAACwD,EAAE,EAAG,aAAYG,OAAO,CAACI,EAAG,EAAE;sBAAArC,QAAA,EACjCiC,OAAO,CAACU;oBAAI;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBACR;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACI,eACbjB,OAAA;oBAAKuC,SAAS,EAAC,eAAe;oBAAApB,QAAA,EAC3Ba,WAAW,CAACoB,OAAO,CAACnB,KAAK;kBAAC;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACvB,eACNjB,OAAA;oBAAKuC,SAAS,EAAC,gBAAgB;oBAAApB,QAAA,gBAC7BnB,OAAA;sBAAGuC,SAAS,EAAC;oBAAa;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAK,EAC9BmC,OAAO,CAACxB,MAAM,IAAI,CAAC,EAAC,IAAE,EAACwB,OAAO,CAACW,UAAU,IAAI,CAAC,EAAC,GAClD;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAM;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP,GAvBCmC,OAAO,CAACI,EAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAyBrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACE,eAGVjB,OAAA;UAASuC,SAAS,EAAC,MAAM;UAAApB,QAAA,gBACvBnB,OAAA;YAAIuC,SAAS,EAAC,eAAe;YAAApB,QAAA,EAAC;UAAY;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAC/CjB,OAAA,CAACb,GAAG;YAAAgC,QAAA,EACDU,WAAW,CAACsB,GAAG,CAACC,OAAO,iBACtBpD,OAAA,CAACZ,GAAG;cAAkBiE,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAChB,SAAS,EAAC,MAAM;cAAApB,QAAA,eACzDnB,OAAA,CAACV,IAAI;gBAACiD,SAAS,EAAC,oBAAoB;gBAAApB,QAAA,gBAClCnB,OAAA,CAACP,IAAI;kBAACwD,EAAE,EAAG,aAAYG,OAAO,CAACI,EAAG,EAAE;kBAAArC,QAAA,eAClCnB,OAAA,CAACV,IAAI,CAACmE,GAAG;oBACPvC,OAAO,EAAC,KAAK;oBACbwC,GAAG,EAAEN,OAAO,CAACO,KAAM;oBACnBpB,SAAS,EAAC;kBAAe;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACzB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACG,eACPjB,OAAA,CAACV,IAAI,CAACsE,IAAI;kBAAAzC,QAAA,gBACRnB,OAAA,CAACV,IAAI,CAACuE,KAAK;oBAACtB,SAAS,EAAC,eAAe;oBAAApB,QAAA,eACnCnB,OAAA,CAACP,IAAI;sBAACwD,EAAE,EAAG,aAAYG,OAAO,CAACI,EAAG,EAAE;sBAAArC,QAAA,EACjCiC,OAAO,CAACU;oBAAI;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBACR;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACI,eACbjB,OAAA;oBAAKuC,SAAS,EAAC,eAAe;oBAAApB,QAAA,EAC3Ba,WAAW,CAACoB,OAAO,CAACnB,KAAK;kBAAC;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACvB,eACNjB,OAAA;oBAAKuC,SAAS,EAAC,gBAAgB;oBAAApB,QAAA,gBAC7BnB,OAAA;sBAAGuC,SAAS,EAAC;oBAAa;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAK,EAC9BmC,OAAO,CAACxB,MAAM,IAAI,CAAC,EAAC,IAAE,EAACwB,OAAO,CAACW,UAAU,IAAI,CAAC,EAAC,GAClD;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAM;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP,GAvBCmC,OAAO,CAACI,EAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAyBrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACA,eAGZjB,OAAA,CAACF,QAAQ;QACPkE,IAAI,EAAEtD,YAAa;QACnBuD,MAAM,EAAEA,CAAA,KAAMtD,eAAe,CAAC,KAAK;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACrC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACQ;AAEpB;AAACf,EAAA,CAtLQD,QAAQ;AAAAiE,EAAA,GAARjE,QAAQ;AAwLjB,eAAeA,QAAQ;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}