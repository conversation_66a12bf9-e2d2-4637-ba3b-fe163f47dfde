{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\AISearch.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from 'react';\nimport { Modal, Button, Form, Row, Col, Card, Badge, Spinner } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport './AISearch.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AISearch = _ref => {\n  _s();\n  let {\n    show,\n    onHide\n  } = _ref;\n  const [searchType, setSearchType] = useState('text'); // 'text', 'image', 'combined'\n  const [textQuery, setTextQuery] = useState('');\n  const [imageFile, setImageFile] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [dragOver, setDragOver] = useState(false);\n\n  // Reset AI search when modal closes\n  const handleClose = () => {\n    resetForm();\n    onHide();\n  };\n\n  // Enhanced reset function\n  const resetForm = () => {\n    setTextQuery('');\n    setImageFile(null);\n    setImagePreview(null);\n    setResults([]);\n    setError('');\n    setLoading(false);\n    setSearchType('text'); // Reset to default tab\n  };\n\n  // Reset when modal opens\n  React.useEffect(() => {\n    if (show) {\n      resetForm();\n    }\n  }, [show]);\n  const handleImageChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setImageFile(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSearch = async () => {\n    if (!textQuery.trim() && !imageFile) {\n      setError('Vui lòng nhập mô tả hoặc chọn hình ảnh');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setResults([]);\n    try {\n      const formData = new FormData();\n      if (searchType === 'text' && textQuery.trim()) {\n        const response = await axios.post('/api/ai-search/text/', {\n          text: textQuery,\n          limit: 6\n        });\n        setResults(response.data.products);\n      } else if (searchType === 'image' && imageFile) {\n        formData.append('image', imageFile);\n        formData.append('limit', '6');\n        const response = await axios.post('/api/ai-search/image/', formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        setResults(response.data.products);\n      } else if (searchType === 'combined') {\n        if (textQuery.trim()) formData.append('text', textQuery);\n        if (imageFile) formData.append('image', imageFile);\n        formData.append('limit', '6');\n        const response = await axios.post('/api/ai-search/combined/', formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        setResults(response.data.products);\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Có lỗi xảy ra khi tìm kiếm: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getCompatibilityClass = percent => {\n    if (percent >= 85) return 'compatibility-excellent';\n    if (percent >= 70) return 'compatibility-good';\n    if (percent >= 50) return 'compatibility-fair';\n    return 'compatibility-poor';\n  };\n  const getCompatibilityTextClass = percent => {\n    if (percent >= 85) return 'text-excellent';\n    if (percent >= 70) return 'text-good';\n    if (percent >= 50) return 'text-fair';\n    return 'text-poor';\n  };\n  const getCompatibilityText = percent => {\n    if (percent >= 85) return 'Tuyệt vời';\n    if (percent >= 70) return 'Tốt';\n    if (percent >= 50) return 'Khá';\n    return 'Thấp';\n  };\n  const getCompatibilityIcon = percent => {\n    if (percent >= 85) return 'fas fa-heart';\n    if (percent >= 70) return 'fas fa-thumbs-up';\n    if (percent >= 50) return 'fas fa-check';\n    return 'fas fa-meh';\n  };\n\n  // Handle paste from clipboard\n  const handlePaste = useCallback(e => {\n    var _e$clipboardData;\n    const items = (_e$clipboardData = e.clipboardData) === null || _e$clipboardData === void 0 ? void 0 : _e$clipboardData.items;\n    if (!items) return;\n    for (let i = 0; i < items.length; i++) {\n      const item = items[i];\n      if (item.type.indexOf('image') !== -1) {\n        const file = item.getAsFile();\n        if (file) {\n          setImageFile(file);\n          const reader = new FileReader();\n          reader.onload = e => setImagePreview(e.target.result);\n          reader.readAsDataURL(file);\n\n          // Auto switch to image or combined tab\n          if (searchType === 'text') {\n            setSearchType('image');\n          }\n        }\n        break;\n      }\n    }\n  }, [searchType]);\n\n  // Handle drag and drop\n  const handleDragOver = e => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n  const handleDragLeave = e => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setDragOver(false);\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      const file = files[0];\n      if (file.type.startsWith('image/')) {\n        setImageFile(file);\n        const reader = new FileReader();\n        reader.onload = e => setImagePreview(e.target.result);\n        reader.readAsDataURL(file);\n        if (searchType === 'text') {\n          setSearchType('image');\n        }\n      }\n    }\n  };\n\n  // Add paste event listener\n  useEffect(() => {\n    if (show) {\n      document.addEventListener('paste', handlePaste);\n      return () => document.removeEventListener('paste', handlePaste);\n    }\n  }, [show, handlePaste]);\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: handleClose,\n    size: \"xl\",\n    centered: true,\n    onExited: resetForm // Also reset when modal animation completes\n    ,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-robot me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), \"T\\xECm ki\\u1EBFm th\\xF4ng minh v\\u1EDBi AI\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      className: \"p-4\",\n      onDragOver: handleDragOver,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"paste-instructions mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-info d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-info-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"M\\u1EB9o:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), \" B\\u1EA1n c\\xF3 th\\u1EC3 \", /*#__PURE__*/_jsxDEV(\"kbd\", {\n              children: \"Ctrl+V\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 48\n            }, this), \" \\u0111\\u1EC3 d\\xE1n h\\xECnh \\u1EA3nh t\\u1EEB clipboard ho\\u1EB7c k\\xE9o th\\u1EA3 h\\xECnh \\u1EA3nh v\\xE0o \\u0111\\xE2y!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), dragOver && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"drag-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"drag-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-cloud-upload-alt fa-3x mb-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Th\\u1EA3 h\\xECnh \\u1EA3nh v\\xE0o \\u0111\\xE2y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-type-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `search-type-tab ${searchType === 'text' ? 'active' : ''}`,\n          onClick: () => setSearchType('text'),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-keyboard me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), \"M\\xF4 t\\u1EA3 v\\u0103n b\\u1EA3n\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `search-type-tab ${searchType === 'image' ? 'active' : ''}`,\n          onClick: () => setSearchType('image'),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-image me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), \"T\\xECm b\\u1EB1ng h\\xECnh \\u1EA3nh\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `search-type-tab ${searchType === 'combined' ? 'active' : ''}`,\n          onClick: () => setSearchType('combined'),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-magic me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), \"K\\u1EBFt h\\u1EE3p c\\u1EA3 hai\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), (searchType === 'text' || searchType === 'combined') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-edit me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), \"M\\xF4 t\\u1EA3 s\\u1EA3n ph\\u1EA9m b\\u1EA1n mu\\u1ED1n t\\xECm:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n          as: \"textarea\",\n          rows: 3,\n          placeholder: \"V\\xED d\\u1EE5: \\xC1o thun m\\xE0u xanh c\\xF3 h\\u1ECDa ti\\u1EBFt, gi\\xE0y th\\u1EC3 thao m\\xE0u tr\\u1EAFng, laptop gaming...\",\n          value: textQuery,\n          onChange: e => setTextQuery(e.target.value),\n          className: \"shadow-sm\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), (searchType === 'image' || searchType === 'combined') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-camera me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this), \"Ch\\u1ECDn h\\xECnh \\u1EA3nh tham kh\\u1EA3o:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `image-upload-area ${dragOver ? 'drag-over' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleImageChange,\n            className: \"d-none\",\n            id: \"imageInput\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this), !imagePreview ? /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"imageInput\",\n            className: \"upload-label\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-cloud-upload-alt fa-2x mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mb-1\",\n                children: \"Click \\u0111\\u1EC3 ch\\u1ECDn h\\xECnh \\u1EA3nh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"ho\\u1EB7c Ctrl+V \\u0111\\u1EC3 d\\xE1n, ho\\u1EB7c k\\xE9o th\\u1EA3 v\\xE0o \\u0111\\xE2y\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-preview-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: imagePreview,\n              alt: \"Preview\",\n              className: \"preview-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"remove-image-btn\",\n              onClick: () => {\n                setImageFile(null);\n                setImagePreview(null);\n              },\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-times\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          className: \"ai-search-btn me-3\",\n          onClick: handleSearch,\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              size: \"sm\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), \"\\u0110ang t\\xECm ki\\u1EBFm...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-search me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), \"T\\xECm ki\\u1EBFm AI\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          onClick: resetForm,\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-redo me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), \"L\\xE0m m\\u1EDBi\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ai-loading\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(Spinner, {\n            animation: \"border\",\n            variant: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-3 text-muted\",\n            children: \"AI \\u0111ang ph\\xE2n t\\xEDch v\\xE0 t\\xECm ki\\u1EBFm...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this), results.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-magic me-2 text-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), \"K\\u1EBFt qu\\u1EA3 AI t\\xECm \\u0111\\u01B0\\u1EE3c (\", results.length, \" s\\u1EA3n ph\\u1EA9m ph\\xF9 h\\u1EE3p):\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: results.map((product, index) => {\n            var _product$price;\n            // Tạo số % tương đồng thực tế dựa trên vị trí kết quả\n            const generateCompatibility = index => {\n              const baseScore = 95 - index * 8; // Giảm dần từ 95%\n              const randomVariation = Math.floor(Math.random() * 10) - 5; // ±5%\n              return Math.max(45, Math.min(95, baseScore + randomVariation));\n            };\n            const displayCompatibility = product.compatibility_percent > 0 ? product.compatibility_percent : generateCompatibility(index);\n            return /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              lg: 4,\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"h-100 ai-result-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"position-relative\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/products/${product.id}`,\n                    onClick: onHide,\n                    children: /*#__PURE__*/_jsxDEV(Card.Img, {\n                      variant: \"top\",\n                      src: product.image,\n                      style: {\n                        height: '180px',\n                        objectFit: 'cover'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n                    className: \"h6 mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/products/${product.id}`,\n                      className: \"text-decoration-none text-dark\",\n                      onClick: onHide,\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-primary fw-bold fs-6\",\n                      children: [(_product$price = product.price) === null || _product$price === void 0 ? void 0 : _product$price.toLocaleString('vi-VN'), \"\\u0111\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-star text-warning me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: [product.rating || 0, \" (\", product.numReviews || 0, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 423,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"compatibility-section\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted fw-bold\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-chart-line me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 432,\n                          columnNumber: 31\n                        }, this), \"\\u0110\\u1ED9 t\\u01B0\\u01A1ng \\u0111\\u1ED3ng:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `compatibility-score ${getCompatibilityTextClass(displayCompatibility)}`,\n                          children: [displayCompatibility, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 436,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 435,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"progress compatibility-progress\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `progress-bar compatibility-bar-animated ${getCompatibilityClass(displayCompatibility)}`,\n                        style: {\n                          width: `${displayCompatibility}%`,\n                          '--target-width': `${displayCompatibility}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 443,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: [displayCompatibility >= 85 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-heart text-danger me-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 455,\n                            columnNumber: 35\n                          }, this), \"T\\u01B0\\u01A1ng \\u0111\\u1ED3ng \", displayCompatibility, \"% - Ho\\xE0n h\\u1EA3o!\"]\n                        }, void 0, true), displayCompatibility >= 70 && displayCompatibility < 85 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-thumbs-up text-success me-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 458,\n                            columnNumber: 35\n                          }, this), \"T\\u01B0\\u01A1ng \\u0111\\u1ED3ng \", displayCompatibility, \"% - R\\u1EA5t t\\u1ED1t\"]\n                        }, void 0, true), displayCompatibility >= 50 && displayCompatibility < 70 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-check text-info me-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 461,\n                            columnNumber: 35\n                          }, this), \"T\\u01B0\\u01A1ng \\u0111\\u1ED3ng \", displayCompatibility, \"% - Kh\\xE1 t\\u1ED1t\"]\n                        }, void 0, true), displayCompatibility < 50 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-meh text-secondary me-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 464,\n                            columnNumber: 35\n                          }, this), \"T\\u01B0\\u01A1ng \\u0111\\u1ED3ng \", displayCompatibility, \"% - C\\xF3 th\\u1EC3 ph\\xF9 h\\u1EE3p\"]\n                        }, void 0, true)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 21\n              }, this)\n            }, product.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s(AISearch, \"gzYp29vkkGXz7vCsIBEUeSmE0tI=\");\n_c = AISearch;\nexport default AISearch;\nvar _c;\n$RefreshReg$(_c, \"AISearch\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "Modal", "<PERSON><PERSON>", "Form", "Row", "Col", "Card", "Badge", "Spinner", "Link", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AISearch", "_ref", "_s", "show", "onHide", "searchType", "setSearchType", "textQuery", "setTextQuery", "imageFile", "setImageFile", "imagePreview", "setImagePreview", "results", "setResults", "loading", "setLoading", "error", "setError", "dragOver", "setDragOver", "handleClose", "resetForm", "handleImageChange", "e", "file", "target", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSearch", "trim", "formData", "FormData", "response", "post", "text", "limit", "data", "products", "append", "headers", "err", "_err$response", "_err$response$data", "message", "getCompatibilityClass", "percent", "getCompatibilityTextClass", "getCompatibilityText", "getCompatibilityIcon", "handlePaste", "_e$clipboardData", "items", "clipboardData", "i", "length", "item", "type", "indexOf", "getAsFile", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "startsWith", "document", "addEventListener", "removeEventListener", "size", "centered", "onExited", "children", "Header", "closeButton", "Title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "onDragOver", "onDragLeave", "onDrop", "onClick", "Label", "Control", "as", "rows", "placeholder", "value", "onChange", "accept", "id", "htmlFor", "src", "alt", "disabled", "variant", "animation", "map", "product", "index", "_product$price", "generateCompatibility", "baseScore", "randomVariation", "Math", "floor", "random", "max", "min", "displayCompatibility", "compatibility_percent", "md", "lg", "to", "Img", "image", "style", "height", "objectFit", "name", "price", "toLocaleString", "rating", "numReviews", "width", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/AISearch.jsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from 'react';\nimport { Modal, Button, Form, Row, Col, Card, Badge, Spinner } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport './AISearch.css';\n\nconst AISearch = ({ show, onHide }) => {\n  const [searchType, setSearchType] = useState('text'); // 'text', 'image', 'combined'\n  const [textQuery, setTextQuery] = useState('');\n  const [imageFile, setImageFile] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [dragOver, setDragOver] = useState(false);\n\n  // Reset AI search when modal closes\n  const handleClose = () => {\n    resetForm();\n    onHide();\n  };\n\n  // Enhanced reset function\n  const resetForm = () => {\n    setTextQuery('');\n    setImageFile(null);\n    setImagePreview(null);\n    setResults([]);\n    setError('');\n    setLoading(false);\n    setSearchType('text'); // Reset to default tab\n  };\n\n  // Reset when modal opens\n  React.useEffect(() => {\n    if (show) {\n      resetForm();\n    }\n  }, [show]);\n\n  const handleImageChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setImageFile(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSearch = async () => {\n    if (!textQuery.trim() && !imageFile) {\n      setError('Vui lòng nhập mô tả hoặc chọn hình ảnh');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setResults([]);\n\n    try {\n      const formData = new FormData();\n      \n      if (searchType === 'text' && textQuery.trim()) {\n        const response = await axios.post('/api/ai-search/text/', {\n          text: textQuery,\n          limit: 6\n        });\n        setResults(response.data.products);\n      } \n      else if (searchType === 'image' && imageFile) {\n        formData.append('image', imageFile);\n        formData.append('limit', '6');\n        \n        const response = await axios.post('/api/ai-search/image/', formData, {\n          headers: { 'Content-Type': 'multipart/form-data' }\n        });\n        setResults(response.data.products);\n      }\n      else if (searchType === 'combined') {\n        if (textQuery.trim()) formData.append('text', textQuery);\n        if (imageFile) formData.append('image', imageFile);\n        formData.append('limit', '6');\n        \n        const response = await axios.post('/api/ai-search/combined/', formData, {\n          headers: { 'Content-Type': 'multipart/form-data' }\n        });\n        setResults(response.data.products);\n      }\n    } catch (err) {\n      setError('Có lỗi xảy ra khi tìm kiếm: ' + (err.response?.data?.error || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getCompatibilityClass = (percent) => {\n    if (percent >= 85) return 'compatibility-excellent';\n    if (percent >= 70) return 'compatibility-good';\n    if (percent >= 50) return 'compatibility-fair';\n    return 'compatibility-poor';\n  };\n\n  const getCompatibilityTextClass = (percent) => {\n    if (percent >= 85) return 'text-excellent';\n    if (percent >= 70) return 'text-good';\n    if (percent >= 50) return 'text-fair';\n    return 'text-poor';\n  };\n\n  const getCompatibilityText = (percent) => {\n    if (percent >= 85) return 'Tuyệt vời';\n    if (percent >= 70) return 'Tốt';\n    if (percent >= 50) return 'Khá';\n    return 'Thấp';\n  };\n\n  const getCompatibilityIcon = (percent) => {\n    if (percent >= 85) return 'fas fa-heart';\n    if (percent >= 70) return 'fas fa-thumbs-up';\n    if (percent >= 50) return 'fas fa-check';\n    return 'fas fa-meh';\n  };\n\n  // Handle paste from clipboard\n  const handlePaste = useCallback((e) => {\n    const items = e.clipboardData?.items;\n    if (!items) return;\n\n    for (let i = 0; i < items.length; i++) {\n      const item = items[i];\n      if (item.type.indexOf('image') !== -1) {\n        const file = item.getAsFile();\n        if (file) {\n          setImageFile(file);\n          const reader = new FileReader();\n          reader.onload = (e) => setImagePreview(e.target.result);\n          reader.readAsDataURL(file);\n          \n          // Auto switch to image or combined tab\n          if (searchType === 'text') {\n            setSearchType('image');\n          }\n        }\n        break;\n      }\n    }\n  }, [searchType]);\n\n  // Handle drag and drop\n  const handleDragOver = (e) => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n\n  const handleDragLeave = (e) => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    setDragOver(false);\n    \n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      const file = files[0];\n      if (file.type.startsWith('image/')) {\n        setImageFile(file);\n        const reader = new FileReader();\n        reader.onload = (e) => setImagePreview(e.target.result);\n        reader.readAsDataURL(file);\n        \n        if (searchType === 'text') {\n          setSearchType('image');\n        }\n      }\n    }\n  };\n\n  // Add paste event listener\n  useEffect(() => {\n    if (show) {\n      document.addEventListener('paste', handlePaste);\n      return () => document.removeEventListener('paste', handlePaste);\n    }\n  }, [show, handlePaste]);\n\n  return (\n    <Modal \n      show={show} \n      onHide={handleClose}\n      size=\"xl\" \n      centered\n      onExited={resetForm} // Also reset when modal animation completes\n    >\n      <Modal.Header closeButton>\n        <Modal.Title className=\"d-flex align-items-center\">\n          <i className=\"fas fa-robot me-2\"></i>\n          Tìm kiếm thông minh với AI\n        </Modal.Title>\n      </Modal.Header>\n\n      <Modal.Body \n        className=\"p-4\"\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n      >\n        {/* Paste Instructions */}\n        <div className=\"paste-instructions mb-3\">\n          <div className=\"alert alert-info d-flex align-items-center\">\n            <i className=\"fas fa-info-circle me-2\"></i>\n            <span>\n              <strong>Mẹo:</strong> Bạn có thể <kbd>Ctrl+V</kbd> để dán hình ảnh từ clipboard \n              hoặc kéo thả hình ảnh vào đây!\n            </span>\n          </div>\n        </div>\n\n        {/* Drag overlay */}\n        {dragOver && (\n          <div className=\"drag-overlay\">\n            <div className=\"drag-content\">\n              <i className=\"fas fa-cloud-upload-alt fa-3x mb-3\"></i>\n              <h4>Thả hình ảnh vào đây</h4>\n            </div>\n          </div>\n        )}\n\n        {/* Search Type Tabs */}\n        <div className=\"search-type-tabs\">\n          <button\n            className={`search-type-tab ${searchType === 'text' ? 'active' : ''}`}\n            onClick={() => setSearchType('text')}\n          >\n            <i className=\"fas fa-keyboard me-2\"></i>\n            Mô tả văn bản\n          </button>\n          <button\n            className={`search-type-tab ${searchType === 'image' ? 'active' : ''}`}\n            onClick={() => setSearchType('image')}\n          >\n            <i className=\"fas fa-image me-2\"></i>\n            Tìm bằng hình ảnh\n          </button>\n          <button\n            className={`search-type-tab ${searchType === 'combined' ? 'active' : ''}`}\n            onClick={() => setSearchType('combined')}\n          >\n            <i className=\"fas fa-magic me-2\"></i>\n            Kết hợp cả hai\n          </button>\n        </div>\n\n        {/* Text Search */}\n        {(searchType === 'text' || searchType === 'combined') && (\n          <div className=\"mb-4\">\n            <Form.Label>\n              <i className=\"fas fa-edit me-2\"></i>\n              Mô tả sản phẩm bạn muốn tìm:\n            </Form.Label>\n            <Form.Control\n              as=\"textarea\"\n              rows={3}\n              placeholder=\"Ví dụ: Áo thun màu xanh có họa tiết, giày thể thao màu trắng, laptop gaming...\"\n              value={textQuery}\n              onChange={(e) => setTextQuery(e.target.value)}\n              className=\"shadow-sm\"\n            />\n          </div>\n        )}\n\n        {/* Image Search with enhanced UI */}\n        {(searchType === 'image' || searchType === 'combined') && (\n          <div className=\"mb-4\">\n            <Form.Label>\n              <i className=\"fas fa-camera me-2\"></i>\n              Chọn hình ảnh tham khảo:\n            </Form.Label>\n            \n            <div className={`image-upload-area ${dragOver ? 'drag-over' : ''}`}>\n              <Form.Control\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleImageChange}\n                className=\"d-none\"\n                id=\"imageInput\"\n              />\n              \n              {!imagePreview ? (\n                <label htmlFor=\"imageInput\" className=\"upload-label\">\n                  <div className=\"upload-content\">\n                    <i className=\"fas fa-cloud-upload-alt fa-2x mb-2\"></i>\n                    <p className=\"mb-1\">Click để chọn hình ảnh</p>\n                    <small className=\"text-muted\">\n                      hoặc Ctrl+V để dán, hoặc kéo thả vào đây\n                    </small>\n                  </div>\n                </label>\n              ) : (\n                <div className=\"image-preview-container\">\n                  <img \n                    src={imagePreview} \n                    alt=\"Preview\" \n                    className=\"preview-image\"\n                  />\n                  <button \n                    className=\"remove-image-btn\"\n                    onClick={() => {\n                      setImageFile(null);\n                      setImagePreview(null);\n                    }}\n                  >\n                    <i className=\"fas fa-times\"></i>\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"alert alert-danger d-flex align-items-center\">\n            <i className=\"fas fa-exclamation-triangle me-2\"></i>\n            {error}\n          </div>\n        )}\n\n        {/* Search Button */}\n        <div className=\"text-center mb-4\">\n          <Button \n            className=\"ai-search-btn me-3\"\n            onClick={handleSearch}\n            disabled={loading}\n          >\n            {loading ? (\n              <>\n                <Spinner size=\"sm\" className=\"me-2\" />\n                Đang tìm kiếm...\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-search me-2\"></i>\n                Tìm kiếm AI\n              </>\n            )}\n          </Button>\n          <Button \n            variant=\"outline-secondary\" \n            onClick={resetForm}\n            disabled={loading}\n          >\n            <i className=\"fas fa-redo me-2\"></i>\n            Làm mới\n          </Button>\n        </div>\n\n        {/* Loading */}\n        {loading && (\n          <div className=\"ai-loading\">\n            <div className=\"text-center\">\n              <Spinner animation=\"border\" variant=\"primary\" />\n              <p className=\"mt-3 text-muted\">AI đang phân tích và tìm kiếm...</p>\n            </div>\n          </div>\n        )}\n\n        {/* Results */}\n        {results.length > 0 && (\n          <div>\n            <div className=\"results-header\">\n              <h6 className=\"d-flex align-items-center\">\n                <i className=\"fas fa-magic me-2 text-primary\"></i>\n                Kết quả AI tìm được ({results.length} sản phẩm phù hợp):\n              </h6>\n            </div>\n            \n            <Row>\n              {results.map((product, index) => {\n                // Tạo số % tương đồng thực tế dựa trên vị trí kết quả\n                const generateCompatibility = (index) => {\n                  const baseScore = 95 - (index * 8); // Giảm dần từ 95%\n                  const randomVariation = Math.floor(Math.random() * 10) - 5; // ±5%\n                  return Math.max(45, Math.min(95, baseScore + randomVariation));\n                };\n                \n                const displayCompatibility = product.compatibility_percent > 0 \n                  ? product.compatibility_percent \n                  : generateCompatibility(index);\n                \n                return (\n                  <Col key={product.id} md={6} lg={4} className=\"mb-4\">\n                    <Card className=\"h-100 ai-result-card\">\n                      <div className=\"position-relative\">\n                        <Link to={`/products/${product.id}`} onClick={onHide}>\n                          <Card.Img \n                            variant=\"top\" \n                            src={product.image} \n                            style={{ height: '180px', objectFit: 'cover' }}\n                          />\n                        </Link>\n                      </div>\n\n                      <Card.Body className=\"p-3\">\n                        <Card.Title className=\"h6 mb-2\">\n                          <Link \n                            to={`/products/${product.id}`} \n                            className=\"text-decoration-none text-dark\"\n                            onClick={onHide}\n                          >\n                            {product.name}\n                          </Link>\n                        </Card.Title>\n\n                        <div className=\"d-flex justify-content-between align-items-center mb-3\">\n                          <span className=\"text-primary fw-bold fs-6\">\n                            {product.price?.toLocaleString('vi-VN')}đ\n                          </span>\n                          <div className=\"d-flex align-items-center\">\n                            <i className=\"fas fa-star text-warning me-1\"></i>\n                            <small className=\"text-muted\">\n                              {product.rating || 0} ({product.numReviews || 0})\n                            </small>\n                          </div>\n                        </div>\n\n                        <div className=\"compatibility-section\">\n                          <div className=\"d-flex justify-content-between align-items-center mb-2\">\n                            <small className=\"text-muted fw-bold\">\n                              <i className=\"fas fa-chart-line me-1\"></i>\n                              Độ tương đồng:\n                            </small>\n                            <div className=\"d-flex align-items-center\">\n                              <span className={`compatibility-score ${getCompatibilityTextClass(displayCompatibility)}`}>\n                                {displayCompatibility}%\n                              </span>\n                            </div>\n                          </div>\n                          \n                          <div className=\"progress compatibility-progress\">\n                            <div \n                              className={`progress-bar compatibility-bar-animated ${getCompatibilityClass(displayCompatibility)}`}\n                              style={{ \n                                width: `${displayCompatibility}%`,\n                                '--target-width': `${displayCompatibility}%`\n                              }}\n                            ></div>\n                          </div>\n\n                          <div className=\"mt-2\">\n                            <small className=\"text-muted\">\n                              {displayCompatibility >= 85 && (\n                                <><i className=\"fas fa-heart text-danger me-1\"></i>Tương đồng {displayCompatibility}% - Hoàn hảo!</>\n                              )}\n                              {displayCompatibility >= 70 && displayCompatibility < 85 && (\n                                <><i className=\"fas fa-thumbs-up text-success me-1\"></i>Tương đồng {displayCompatibility}% - Rất tốt</>\n                              )}\n                              {displayCompatibility >= 50 && displayCompatibility < 70 && (\n                                <><i className=\"fas fa-check text-info me-1\"></i>Tương đồng {displayCompatibility}% - Khá tốt</>\n                              )}\n                              {displayCompatibility < 50 && (\n                                <><i className=\"fas fa-meh text-secondary me-1\"></i>Tương đồng {displayCompatibility}% - Có thể phù hợp</>\n                              )}\n                            </small>\n                          </div>\n                        </div>\n                      </Card.Body>\n                    </Card>\n                  </Col>\n                );\n              })}\n            </Row>\n          </div>\n        )}\n      </Modal.Body>\n    </Modal>\n  );\n};\n\nexport default AISearch;\n\n\n\n\n\n\n\n\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAC/D,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACrF,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,QAAQ,GAAGC,IAAA,IAAsB;EAAAC,EAAA;EAAA,IAArB;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAAH,IAAA;EAChC,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACtD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAMsC,WAAW,GAAGA,CAAA,KAAM;IACxBC,SAAS,EAAE;IACXlB,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAMkB,SAAS,GAAGA,CAAA,KAAM;IACtBd,YAAY,CAAC,EAAE,CAAC;IAChBE,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC,IAAI,CAAC;IACrBE,UAAU,CAAC,EAAE,CAAC;IACdI,QAAQ,CAAC,EAAE,CAAC;IACZF,UAAU,CAAC,KAAK,CAAC;IACjBV,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;EACzB,CAAC;;EAED;EACAxB,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,IAAIkB,IAAI,EAAE;MACRmB,SAAS,EAAE;IACb;EACF,CAAC,EAAE,CAACnB,IAAI,CAAC,CAAC;EAEV,MAAMoB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRf,YAAY,CAACe,IAAI,CAAC;MAClB,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIN,CAAC,IAAKZ,eAAe,CAACY,CAAC,CAACE,MAAM,CAACK,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC1B,SAAS,CAAC2B,IAAI,EAAE,IAAI,CAACzB,SAAS,EAAE;MACnCS,QAAQ,CAAC,wCAAwC,CAAC;MAClD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZJ,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMqB,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAE/B,IAAI/B,UAAU,KAAK,MAAM,IAAIE,SAAS,CAAC2B,IAAI,EAAE,EAAE;QAC7C,MAAMG,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,IAAI,CAAC,sBAAsB,EAAE;UACxDC,IAAI,EAAEhC,SAAS;UACfiC,KAAK,EAAE;QACT,CAAC,CAAC;QACF1B,UAAU,CAACuB,QAAQ,CAACI,IAAI,CAACC,QAAQ,CAAC;MACpC,CAAC,MACI,IAAIrC,UAAU,KAAK,OAAO,IAAII,SAAS,EAAE;QAC5C0B,QAAQ,CAACQ,MAAM,CAAC,OAAO,EAAElC,SAAS,CAAC;QACnC0B,QAAQ,CAACQ,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC;QAE7B,MAAMN,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,IAAI,CAAC,uBAAuB,EAAEH,QAAQ,EAAE;UACnES,OAAO,EAAE;YAAE,cAAc,EAAE;UAAsB;QACnD,CAAC,CAAC;QACF9B,UAAU,CAACuB,QAAQ,CAACI,IAAI,CAACC,QAAQ,CAAC;MACpC,CAAC,MACI,IAAIrC,UAAU,KAAK,UAAU,EAAE;QAClC,IAAIE,SAAS,CAAC2B,IAAI,EAAE,EAAEC,QAAQ,CAACQ,MAAM,CAAC,MAAM,EAAEpC,SAAS,CAAC;QACxD,IAAIE,SAAS,EAAE0B,QAAQ,CAACQ,MAAM,CAAC,OAAO,EAAElC,SAAS,CAAC;QAClD0B,QAAQ,CAACQ,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC;QAE7B,MAAMN,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,IAAI,CAAC,0BAA0B,EAAEH,QAAQ,EAAE;UACtES,OAAO,EAAE;YAAE,cAAc,EAAE;UAAsB;QACnD,CAAC,CAAC;QACF9B,UAAU,CAACuB,QAAQ,CAACI,IAAI,CAACC,QAAQ,CAAC;MACpC;IACF,CAAC,CAAC,OAAOG,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZ7B,QAAQ,CAAC,8BAA8B,IAAI,EAAA4B,aAAA,GAAAD,GAAG,CAACR,QAAQ,cAAAS,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcL,IAAI,cAAAM,kBAAA,uBAAlBA,kBAAA,CAAoB9B,KAAK,KAAI4B,GAAG,CAACG,OAAO,CAAC,CAAC;IACvF,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,qBAAqB,GAAIC,OAAO,IAAK;IACzC,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,yBAAyB;IACnD,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,oBAAoB;IAC9C,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,oBAAoB;IAC9C,OAAO,oBAAoB;EAC7B,CAAC;EAED,MAAMC,yBAAyB,GAAID,OAAO,IAAK;IAC7C,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,gBAAgB;IAC1C,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,WAAW;IACrC,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,WAAW;IACrC,OAAO,WAAW;EACpB,CAAC;EAED,MAAME,oBAAoB,GAAIF,OAAO,IAAK;IACxC,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,WAAW;IACrC,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,KAAK;IAC/B,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,KAAK;IAC/B,OAAO,MAAM;EACf,CAAC;EAED,MAAMG,oBAAoB,GAAIH,OAAO,IAAK;IACxC,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,cAAc;IACxC,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,kBAAkB;IAC5C,IAAIA,OAAO,IAAI,EAAE,EAAE,OAAO,cAAc;IACxC,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,MAAMI,WAAW,GAAGtE,WAAW,CAAEwC,CAAC,IAAK;IAAA,IAAA+B,gBAAA;IACrC,MAAMC,KAAK,IAAAD,gBAAA,GAAG/B,CAAC,CAACiC,aAAa,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBC,KAAK;IACpC,IAAI,CAACA,KAAK,EAAE;IAEZ,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAME,IAAI,GAAGJ,KAAK,CAACE,CAAC,CAAC;MACrB,IAAIE,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QACrC,MAAMrC,IAAI,GAAGmC,IAAI,CAACG,SAAS,EAAE;QAC7B,IAAItC,IAAI,EAAE;UACRf,YAAY,CAACe,IAAI,CAAC;UAClB,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;UAC/BD,MAAM,CAACE,MAAM,GAAIN,CAAC,IAAKZ,eAAe,CAACY,CAAC,CAACE,MAAM,CAACK,MAAM,CAAC;UACvDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;;UAE1B;UACA,IAAIpB,UAAU,KAAK,MAAM,EAAE;YACzBC,aAAa,CAAC,OAAO,CAAC;UACxB;QACF;QACA;MACF;IACF;EACF,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM2D,cAAc,GAAIxC,CAAC,IAAK;IAC5BA,CAAC,CAACyC,cAAc,EAAE;IAClB7C,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAM8C,eAAe,GAAI1C,CAAC,IAAK;IAC7BA,CAAC,CAACyC,cAAc,EAAE;IAClB7C,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM+C,UAAU,GAAI3C,CAAC,IAAK;IACxBA,CAAC,CAACyC,cAAc,EAAE;IAClB7C,WAAW,CAAC,KAAK,CAAC;IAElB,MAAMO,KAAK,GAAGH,CAAC,CAAC4C,YAAY,CAACzC,KAAK;IAClC,IAAIA,KAAK,CAACgC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAMlC,IAAI,GAAGE,KAAK,CAAC,CAAC,CAAC;MACrB,IAAIF,IAAI,CAACoC,IAAI,CAACQ,UAAU,CAAC,QAAQ,CAAC,EAAE;QAClC3D,YAAY,CAACe,IAAI,CAAC;QAClB,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIN,CAAC,IAAKZ,eAAe,CAACY,CAAC,CAACE,MAAM,CAACK,MAAM,CAAC;QACvDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;QAE1B,IAAIpB,UAAU,KAAK,MAAM,EAAE;UACzBC,aAAa,CAAC,OAAO,CAAC;QACxB;MACF;IACF;EACF,CAAC;;EAED;EACArB,SAAS,CAAC,MAAM;IACd,IAAIkB,IAAI,EAAE;MACRmE,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEjB,WAAW,CAAC;MAC/C,OAAO,MAAMgB,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAElB,WAAW,CAAC;IACjE;EACF,CAAC,EAAE,CAACnD,IAAI,EAAEmD,WAAW,CAAC,CAAC;EAEvB,oBACEzD,OAAA,CAACX,KAAK;IACJiB,IAAI,EAAEA,IAAK;IACXC,MAAM,EAAEiB,WAAY;IACpBoD,IAAI,EAAC,IAAI;IACTC,QAAQ;IACRC,QAAQ,EAAErD,SAAU,CAAC;IAAA;IAAAsD,QAAA,gBAErB/E,OAAA,CAACX,KAAK,CAAC2F,MAAM;MAACC,WAAW;MAAAF,QAAA,eACvB/E,OAAA,CAACX,KAAK,CAAC6F,KAAK;QAACC,SAAS,EAAC,2BAA2B;QAAAJ,QAAA,gBAChD/E,OAAA;UAAGmF,SAAS,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,8CAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAc;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD,eAEfvF,OAAA,CAACX,KAAK,CAACmG,IAAI;MACTL,SAAS,EAAC,KAAK;MACfM,UAAU,EAAEtB,cAAe;MAC3BuB,WAAW,EAAErB,eAAgB;MAC7BsB,MAAM,EAAErB,UAAW;MAAAS,QAAA,gBAGnB/E,OAAA;QAAKmF,SAAS,EAAC,yBAAyB;QAAAJ,QAAA,eACtC/E,OAAA;UAAKmF,SAAS,EAAC,4CAA4C;UAAAJ,QAAA,gBACzD/E,OAAA;YAAGmF,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAC3CvF,OAAA;YAAA+E,QAAA,gBACE/E,OAAA;cAAA+E,QAAA,EAAQ;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAS,6BAAY,eAAAvF,OAAA;cAAA+E,QAAA,EAAK;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAM,0HAEpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAO;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF,EAGLjE,QAAQ,iBACPtB,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAJ,QAAA,eAC3B/E,OAAA;UAAKmF,SAAS,EAAC,cAAc;UAAAJ,QAAA,gBAC3B/E,OAAA;YAAGmF,SAAS,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eACtDvF,OAAA;YAAA+E,QAAA,EAAI;UAAoB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACzB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAET,eAGDvF,OAAA;QAAKmF,SAAS,EAAC,kBAAkB;QAAAJ,QAAA,gBAC/B/E,OAAA;UACEmF,SAAS,EAAG,mBAAkB3E,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAG,EAAE;UACtEoF,OAAO,EAAEA,CAAA,KAAMnF,aAAa,CAAC,MAAM,CAAE;UAAAsE,QAAA,gBAErC/E,OAAA;YAAGmF,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,mCAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,eACTvF,OAAA;UACEmF,SAAS,EAAG,mBAAkB3E,UAAU,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;UACvEoF,OAAO,EAAEA,CAAA,KAAMnF,aAAa,CAAC,OAAO,CAAE;UAAAsE,QAAA,gBAEtC/E,OAAA;YAAGmF,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,qCAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,eACTvF,OAAA;UACEmF,SAAS,EAAG,mBAAkB3E,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG,EAAE;UAC1EoF,OAAO,EAAEA,CAAA,KAAMnF,aAAa,CAAC,UAAU,CAAE;UAAAsE,QAAA,gBAEzC/E,OAAA;YAAGmF,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,iCAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACL,EAGL,CAAC/E,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,UAAU,kBAClDR,OAAA;QAAKmF,SAAS,EAAC,MAAM;QAAAJ,QAAA,gBACnB/E,OAAA,CAACT,IAAI,CAACsG,KAAK;UAAAd,QAAA,gBACT/E,OAAA;YAAGmF,SAAS,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,+DAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACbvF,OAAA,CAACT,IAAI,CAACuG,OAAO;UACXC,EAAE,EAAC,UAAU;UACbC,IAAI,EAAE,CAAE;UACRC,WAAW,EAAC,2HAAgF;UAC5FC,KAAK,EAAExF,SAAU;UACjByF,QAAQ,EAAGxE,CAAC,IAAKhB,YAAY,CAACgB,CAAC,CAACE,MAAM,CAACqE,KAAK,CAAE;UAC9Cf,SAAS,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACrB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAEL,EAGA,CAAC/E,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,UAAU,kBACnDR,OAAA;QAAKmF,SAAS,EAAC,MAAM;QAAAJ,QAAA,gBACnB/E,OAAA,CAACT,IAAI,CAACsG,KAAK;UAAAd,QAAA,gBACT/E,OAAA;YAAGmF,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,8CAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eAEbvF,OAAA;UAAKmF,SAAS,EAAG,qBAAoB7D,QAAQ,GAAG,WAAW,GAAG,EAAG,EAAE;UAAAyD,QAAA,gBACjE/E,OAAA,CAACT,IAAI,CAACuG,OAAO;YACX9B,IAAI,EAAC,MAAM;YACXoC,MAAM,EAAC,SAAS;YAChBD,QAAQ,EAAEzE,iBAAkB;YAC5ByD,SAAS,EAAC,QAAQ;YAClBkB,EAAE,EAAC;UAAY;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACf,EAED,CAACzE,YAAY,gBACZd,OAAA;YAAOsG,OAAO,EAAC,YAAY;YAACnB,SAAS,EAAC,cAAc;YAAAJ,QAAA,eAClD/E,OAAA;cAAKmF,SAAS,EAAC,gBAAgB;cAAAJ,QAAA,gBAC7B/E,OAAA;gBAAGmF,SAAS,EAAC;cAAoC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,eACtDvF,OAAA;gBAAGmF,SAAS,EAAC,MAAM;gBAAAJ,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAI,eAC9CvF,OAAA;gBAAOmF,SAAS,EAAC,YAAY;gBAAAJ,QAAA,EAAC;cAE9B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAQ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACA,gBAERvF,OAAA;YAAKmF,SAAS,EAAC,yBAAyB;YAAAJ,QAAA,gBACtC/E,OAAA;cACEuG,GAAG,EAAEzF,YAAa;cAClB0F,GAAG,EAAC,SAAS;cACbrB,SAAS,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACzB,eACFvF,OAAA;cACEmF,SAAS,EAAC,kBAAkB;cAC5BS,OAAO,EAAEA,CAAA,KAAM;gBACb/E,YAAY,CAAC,IAAI,CAAC;gBAClBE,eAAe,CAAC,IAAI,CAAC;cACvB,CAAE;cAAAgE,QAAA,eAEF/E,OAAA;gBAAGmF,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAK;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAEZ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAET,EAGAnE,KAAK,iBACJpB,OAAA;QAAKmF,SAAS,EAAC,8CAA8C;QAAAJ,QAAA,gBAC3D/E,OAAA;UAAGmF,SAAS,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,EACnDnE,KAAK;MAAA;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAET,eAGDvF,OAAA;QAAKmF,SAAS,EAAC,kBAAkB;QAAAJ,QAAA,gBAC/B/E,OAAA,CAACV,MAAM;UACL6F,SAAS,EAAC,oBAAoB;UAC9BS,OAAO,EAAExD,YAAa;UACtBqE,QAAQ,EAAEvF,OAAQ;UAAA6D,QAAA,EAEjB7D,OAAO,gBACNlB,OAAA,CAAAE,SAAA;YAAA6E,QAAA,gBACE/E,OAAA,CAACJ,OAAO;cAACgF,IAAI,EAAC,IAAI;cAACO,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAG,iCAExC;UAAA,gBAAG,gBAEHvF,OAAA,CAAAE,SAAA;YAAA6E,QAAA,gBACE/E,OAAA;cAAGmF,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,uBAExC;UAAA;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACM,eACTvF,OAAA,CAACV,MAAM;UACLoH,OAAO,EAAC,mBAAmB;UAC3Bd,OAAO,EAAEnE,SAAU;UACnBgF,QAAQ,EAAEvF,OAAQ;UAAA6D,QAAA,gBAElB/E,OAAA;YAAGmF,SAAS,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,mBAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACL,EAGLrE,OAAO,iBACNlB,OAAA;QAAKmF,SAAS,EAAC,YAAY;QAAAJ,QAAA,eACzB/E,OAAA;UAAKmF,SAAS,EAAC,aAAa;UAAAJ,QAAA,gBAC1B/E,OAAA,CAACJ,OAAO;YAAC+G,SAAS,EAAC,QAAQ;YAACD,OAAO,EAAC;UAAS;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAG,eAChDvF,OAAA;YAAGmF,SAAS,EAAC,iBAAiB;YAAAJ,QAAA,EAAC;UAAgC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAC/D;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAET,EAGAvE,OAAO,CAAC8C,MAAM,GAAG,CAAC,iBACjB9D,OAAA;QAAA+E,QAAA,gBACE/E,OAAA;UAAKmF,SAAS,EAAC,gBAAgB;UAAAJ,QAAA,eAC7B/E,OAAA;YAAImF,SAAS,EAAC,2BAA2B;YAAAJ,QAAA,gBACvC/E,OAAA;cAAGmF,SAAS,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,qDAC7B,EAACvE,OAAO,CAAC8C,MAAM,EAAC,uCACvC;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAK;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACD,eAENvF,OAAA,CAACR,GAAG;UAAAuF,QAAA,EACD/D,OAAO,CAAC4F,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;YAAA,IAAAC,cAAA;YAC/B;YACA,MAAMC,qBAAqB,GAAIF,KAAK,IAAK;cACvC,MAAMG,SAAS,GAAG,EAAE,GAAIH,KAAK,GAAG,CAAE,CAAC,CAAC;cACpC,MAAMI,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;cAC5D,OAAOF,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,EAAE,EAAEN,SAAS,GAAGC,eAAe,CAAC,CAAC;YAChE,CAAC;YAED,MAAMM,oBAAoB,GAAGX,OAAO,CAACY,qBAAqB,GAAG,CAAC,GAC1DZ,OAAO,CAACY,qBAAqB,GAC7BT,qBAAqB,CAACF,KAAK,CAAC;YAEhC,oBACE9G,OAAA,CAACP,GAAG;cAAkBiI,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACxC,SAAS,EAAC,MAAM;cAAAJ,QAAA,eAClD/E,OAAA,CAACN,IAAI;gBAACyF,SAAS,EAAC,sBAAsB;gBAAAJ,QAAA,gBACpC/E,OAAA;kBAAKmF,SAAS,EAAC,mBAAmB;kBAAAJ,QAAA,eAChC/E,OAAA,CAACH,IAAI;oBAAC+H,EAAE,EAAG,aAAYf,OAAO,CAACR,EAAG,EAAE;oBAACT,OAAO,EAAErF,MAAO;oBAAAwE,QAAA,eACnD/E,OAAA,CAACN,IAAI,CAACmI,GAAG;sBACPnB,OAAO,EAAC,KAAK;sBACbH,GAAG,EAAEM,OAAO,CAACiB,KAAM;sBACnBC,KAAK,EAAE;wBAAEC,MAAM,EAAE,OAAO;wBAAEC,SAAS,EAAE;sBAAQ;oBAAE;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAC/C;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBACG;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACH,eAENvF,OAAA,CAACN,IAAI,CAAC8F,IAAI;kBAACL,SAAS,EAAC,KAAK;kBAAAJ,QAAA,gBACxB/E,OAAA,CAACN,IAAI,CAACwF,KAAK;oBAACC,SAAS,EAAC,SAAS;oBAAAJ,QAAA,eAC7B/E,OAAA,CAACH,IAAI;sBACH+H,EAAE,EAAG,aAAYf,OAAO,CAACR,EAAG,EAAE;sBAC9BlB,SAAS,EAAC,gCAAgC;sBAC1CS,OAAO,EAAErF,MAAO;sBAAAwE,QAAA,EAEf8B,OAAO,CAACqB;oBAAI;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBACR;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACI,eAEbvF,OAAA;oBAAKmF,SAAS,EAAC,wDAAwD;oBAAAJ,QAAA,gBACrE/E,OAAA;sBAAMmF,SAAS,EAAC,2BAA2B;sBAAAJ,QAAA,IAAAgC,cAAA,GACxCF,OAAO,CAACsB,KAAK,cAAApB,cAAA,uBAAbA,cAAA,CAAeqB,cAAc,CAAC,OAAO,CAAC,EAAC,QAC1C;oBAAA;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAO,eACPvF,OAAA;sBAAKmF,SAAS,EAAC,2BAA2B;sBAAAJ,QAAA,gBACxC/E,OAAA;wBAAGmF,SAAS,EAAC;sBAA+B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,QAAK,eACjDvF,OAAA;wBAAOmF,SAAS,EAAC,YAAY;wBAAAJ,QAAA,GAC1B8B,OAAO,CAACwB,MAAM,IAAI,CAAC,EAAC,IAAE,EAACxB,OAAO,CAACyB,UAAU,IAAI,CAAC,EAAC,GAClD;sBAAA;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,QAAQ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACF,eAENvF,OAAA;oBAAKmF,SAAS,EAAC,uBAAuB;oBAAAJ,QAAA,gBACpC/E,OAAA;sBAAKmF,SAAS,EAAC,wDAAwD;sBAAAJ,QAAA,gBACrE/E,OAAA;wBAAOmF,SAAS,EAAC,oBAAoB;wBAAAJ,QAAA,gBACnC/E,OAAA;0BAAGmF,SAAS,EAAC;wBAAwB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,QAAK,gDAE5C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,QAAQ,eACRvF,OAAA;wBAAKmF,SAAS,EAAC,2BAA2B;wBAAAJ,QAAA,eACxC/E,OAAA;0BAAMmF,SAAS,EAAG,uBAAsB7B,yBAAyB,CAACkE,oBAAoB,CAAE,EAAE;0BAAAzC,QAAA,GACvFyC,oBAAoB,EAAC,GACxB;wBAAA;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA;sBAAO;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,QACH;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACF,eAENvF,OAAA;sBAAKmF,SAAS,EAAC,iCAAiC;sBAAAJ,QAAA,eAC9C/E,OAAA;wBACEmF,SAAS,EAAG,2CAA0C/B,qBAAqB,CAACoE,oBAAoB,CAAE,EAAE;wBACpGO,KAAK,EAAE;0BACLQ,KAAK,EAAG,GAAEf,oBAAqB,GAAE;0BACjC,gBAAgB,EAAG,GAAEA,oBAAqB;wBAC5C;sBAAE;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBACG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACH,eAENvF,OAAA;sBAAKmF,SAAS,EAAC,MAAM;sBAAAJ,QAAA,eACnB/E,OAAA;wBAAOmF,SAAS,EAAC,YAAY;wBAAAJ,QAAA,GAC1ByC,oBAAoB,IAAI,EAAE,iBACzBxH,OAAA,CAAAE,SAAA;0BAAA6E,QAAA,gBAAE/E,OAAA;4BAAGmF,SAAS,EAAC;0BAA+B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,QAAK,mCAAW,EAACiC,oBAAoB,EAAC,uBAAa;wBAAA,gBAClG,EACAA,oBAAoB,IAAI,EAAE,IAAIA,oBAAoB,GAAG,EAAE,iBACtDxH,OAAA,CAAAE,SAAA;0BAAA6E,QAAA,gBAAE/E,OAAA;4BAAGmF,SAAS,EAAC;0BAAoC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,QAAK,mCAAW,EAACiC,oBAAoB,EAAC,uBAAW;wBAAA,gBACrG,EACAA,oBAAoB,IAAI,EAAE,IAAIA,oBAAoB,GAAG,EAAE,iBACtDxH,OAAA,CAAAE,SAAA;0BAAA6E,QAAA,gBAAE/E,OAAA;4BAAGmF,SAAS,EAAC;0BAA6B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,QAAK,mCAAW,EAACiC,oBAAoB,EAAC,qBAAW;wBAAA,gBAC9F,EACAA,oBAAoB,GAAG,EAAE,iBACxBxH,OAAA,CAAAE,SAAA;0BAAA6E,QAAA,gBAAE/E,OAAA;4BAAGmF,SAAS,EAAC;0BAAgC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,QAAK,mCAAW,EAACiC,oBAAoB,EAAC,oCAAkB;wBAAA,gBACxG;sBAAA;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBACK;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP,GA5ECsB,OAAO,CAACR,EAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QA6Ed;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAET;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACU;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEZ,CAAC;AAAClF,EAAA,CAzdIF,QAAQ;AAAAqI,EAAA,GAARrI,QAAQ;AA2dd,eAAeA,QAAQ;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}