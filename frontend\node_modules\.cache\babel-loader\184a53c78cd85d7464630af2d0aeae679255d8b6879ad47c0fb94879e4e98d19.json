{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\context\\\\userContext.js\",\n  _s = $RefreshSig$();\nimport { createContext, useState, useEffect } from \"react\";\nimport httpService from \"../services/httpService\";\nimport Loader from \"../components/loader\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserContext = /*#__PURE__*/createContext();\nexport default UserContext;\nexport const UserProvider = _ref => {\n  _s();\n  let {\n    children\n  } = _ref;\n  const [authTokens, setAuthTokens] = useState(localStorage.getItem(\"authTokens\") ? JSON.parse(localStorage.getItem(\"authTokens\")) : null);\n  const [userInfo, setUserInfo] = useState(localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")) : null);\n  const [error, setError] = useState(\"\");\n  const [loading, setLoading] = useState(true);\n  const login = async (username, password) => {\n    try {\n      const {\n        data\n      } = await httpService.post(\"/auth/jwt/create/\", {\n        username,\n        password\n      });\n      setAuthTokens({\n        access: data.access,\n        refresh: data.refresh\n      });\n      localStorage.setItem(\"authTokens\", JSON.stringify({\n        access: data.access,\n        refresh: data.refresh\n      }));\n\n      // Gọi tiếp API lấy thông tin user đầy đủ\n      httpService.setJwt(data.access); // Đảm bảo token đã set cho request tiếp theo\n      const {\n        data: userData\n      } = await httpService.get(\"/auth/users/me/\");\n      console.log(\"Login - User data from server:\", userData);\n\n      // Get existing profile data from localStorage if available\n      const existingUserInfo = localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")) : {};\n      console.log(\"Login - Existing user info from localStorage:\", existingUserInfo);\n      const fullUserInfo = {\n        id: userData.id,\n        username: userData.username,\n        email: userData.email,\n        // Use isAdmin from userData (server response) or fallback to JWT data\n        isAdmin: userData.isAdmin !== undefined ? userData.isAdmin : data.isAdmin,\n        // Merge server data with existing localStorage data\n        first_name: userData.first_name || existingUserInfo.first_name || \"\",\n        last_name: userData.last_name || existingUserInfo.last_name || \"\",\n        phone: userData.phone || existingUserInfo.phone || \"\",\n        gender: userData.gender || existingUserInfo.gender || \"\",\n        birth_date: userData.birth_date || existingUserInfo.birth_date || \"\",\n        address: userData.address || existingUserInfo.address || \"\",\n        avatar: userData.avatar || existingUserInfo.avatar || null,\n        profileFetched: true // Mark as fetched to prevent infinite loop\n      };\n\n      console.log(\"Login - Setting full user info:\", fullUserInfo);\n      console.log(\"Login - isAdmin debug:\", {\n        \"userData.isAdmin\": userData.isAdmin,\n        \"data.isAdmin\": data.isAdmin,\n        \"final isAdmin\": fullUserInfo.isAdmin,\n        \"isAdmin type\": typeof fullUserInfo.isAdmin\n      });\n      setUserInfo(fullUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(fullUserInfo));\n      setError(\"\");\n      return true;\n    } catch (ex) {\n      var _ex$response;\n      setError({\n        login: (_ex$response = ex.response) === null || _ex$response === void 0 ? void 0 : _ex$response.data\n      });\n      return false;\n    }\n  };\n  const register = async (username, email, password) => {\n    try {\n      const {\n        data\n      } = await httpService.post(\"/auth/users/\", {\n        username,\n        email,\n        password\n      });\n      await login(username, password);\n      return true;\n    } catch (ex) {\n      setError({\n        register: ex.response.data\n      });\n      return false;\n    }\n  };\n  const logout = () => {\n    setAuthTokens(null);\n    setUserInfo(null);\n    localStorage.removeItem(\"authTokens\");\n    // Keep userInfo in localStorage for next login - only remove auth tokens\n    // localStorage.removeItem(\"userInfo\"); // Comment this out to preserve profile data\n    console.log(\"Logout - Keeping userInfo in localStorage for next login\");\n    // httpService.setJwt(undefined)\n  };\n\n  const refresh = async () => {\n    try {\n      const {\n        data\n      } = await httpService.post(\"/auth/jwt/refresh/\", {\n        refresh: authTokens.refresh\n      });\n      // httpService.setJwt(data.access)\n      setAuthTokens({\n        access: data.access,\n        refresh: data.refresh\n      });\n      localStorage.setItem(\"authTokens\", JSON.stringify({\n        access: data.access,\n        refresh: data.refresh\n      }));\n    } catch (ex) {\n      var _ex$response2, _ex$response3;\n      // ✅ Chỉ logout khi refresh token thực sự hết hạn\n      console.error(\"Token refresh failed:\", (_ex$response2 = ex.response) === null || _ex$response2 === void 0 ? void 0 : _ex$response2.status);\n      if (((_ex$response3 = ex.response) === null || _ex$response3 === void 0 ? void 0 : _ex$response3.status) === 401) {\n        logout();\n      }\n    }\n  };\n  useEffect(() => {\n    if (authTokens) {\n      refresh();\n    }\n    setLoading(false);\n  }, []);\n  useEffect(() => {\n    httpService.setJwt(authTokens && authTokens.access ? authTokens.access : null);\n  }, [loading, authTokens]);\n\n  // Separate useEffect for fetching profile to avoid infinite loop\n  useEffect(() => {\n    // If userInfo exists but missing profile fields, fetch from server\n    if (authTokens && userInfo && userInfo.id && (!userInfo.first_name || userInfo.first_name === \"\") && (!userInfo.last_name || userInfo.last_name === \"\") && (!userInfo.phone || userInfo.phone === \"\") && !userInfo.profileFetched // Add flag to prevent infinite loop\n    ) {\n      console.log(\"UserInfo missing profile data, fetching from server...\");\n      fetchUserProfile();\n    }\n  }, [authTokens, userInfo]);\n\n  // Helper function to fetch user profile\n  const fetchUserProfile = async () => {\n    try {\n      const {\n        data: userData\n      } = await httpService.get(\"/auth/users/me/\");\n      console.log(\"Fetched user profile:\", userData);\n      const updatedUserInfo = {\n        ...userInfo,\n        first_name: userData.first_name || \"\",\n        last_name: userData.last_name || \"\",\n        phone: userData.phone || \"\",\n        gender: userData.gender || \"\",\n        birth_date: userData.birth_date || \"\",\n        address: userData.address || \"\",\n        avatar: userData.avatar || (userInfo === null || userInfo === void 0 ? void 0 : userInfo.avatar) || null,\n        profileFetched: true // Add flag to prevent infinite loop\n      };\n\n      console.log(\"Updated user info with profile:\", updatedUserInfo);\n      setUserInfo(updatedUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n    } catch (error) {\n      console.error(\"Failed to fetch user profile:\", error);\n      // Set flag even on error to prevent infinite retry\n      const updatedUserInfo = {\n        ...userInfo,\n        profileFetched: true\n      };\n      setUserInfo(updatedUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n    }\n  };\n  useEffect(() => {\n    let timeInterval = 1000 * 60 * 60; // Refresh tokens after every 1 hour\n    const interval = setInterval(() => {\n      if (authTokens) refresh();\n    }, timeInterval);\n    return () => clearInterval(interval);\n  }, [authTokens]);\n  const updateProfile = async profileData => {\n    try {\n      console.log(\"Updating profile with data:\", profileData);\n      console.log(\"Current userInfo:\", userInfo);\n      let payload = {};\n\n      // Always include profile fields (allow empty strings)\n      payload.first_name = profileData.firstName || \"\";\n      payload.last_name = profileData.lastName || \"\";\n      payload.phone = profileData.phone || \"\";\n      payload.gender = profileData.gender || \"\";\n      payload.birth_date = profileData.birthDate || \"\";\n      payload.address = profileData.address || \"\";\n\n      // Only include username/email/password if they have values and are different\n      if (profileData.username && profileData.username !== userInfo.username) {\n        payload.username = profileData.username;\n      }\n      if (profileData.email && profileData.email !== userInfo.email) {\n        payload.email = profileData.email;\n      }\n      if (profileData.password && profileData.password !== \"\") {\n        payload.password = profileData.password;\n      }\n      console.log(\"Sending payload:\", payload);\n      const {\n        data\n      } = await httpService.patch(\"/auth/users/me/\", payload);\n      console.log(\"Update response:\", data);\n\n      // Update userInfo with new data - preserve existing data if server doesn't return it\n      const updatedUserInfo = {\n        ...userInfo,\n        username: data.username || userInfo.username,\n        email: data.email || userInfo.email,\n        first_name: data.first_name !== undefined ? data.first_name : payload.first_name,\n        last_name: data.last_name !== undefined ? data.last_name : payload.last_name,\n        phone: data.phone !== undefined ? data.phone : payload.phone,\n        gender: data.gender !== undefined ? data.gender : payload.gender,\n        birth_date: data.birth_date !== undefined ? data.birth_date : payload.birth_date,\n        address: data.address !== undefined ? data.address : payload.address,\n        profileFetched: true // Mark as having profile data\n      };\n\n      console.log(\"Updated userInfo:\", updatedUserInfo);\n      setUserInfo(updatedUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n      setError(\"\");\n      return true;\n    } catch (ex) {\n      var _ex$response4, _ex$response5, _ex$response5$data;\n      console.error(\"Update profile error:\", ex);\n      console.error(\"Error response:\", (_ex$response4 = ex.response) === null || _ex$response4 === void 0 ? void 0 : _ex$response4.data);\n      setError(((_ex$response5 = ex.response) === null || _ex$response5 === void 0 ? void 0 : (_ex$response5$data = _ex$response5.data) === null || _ex$response5$data === void 0 ? void 0 : _ex$response5$data.message) || \"Cập nhật thông tin thất bại\");\n      return false;\n    }\n  };\n  const uploadAvatar = async avatarFile => {\n    try {\n      const formData = new FormData();\n      formData.append(\"avatar\", avatarFile);\n      console.log(\"Uploading avatar file:\", avatarFile);\n      console.log(\"FormData:\", formData);\n\n      // Try different endpoints for avatar upload\n      let response;\n      let avatarUrl;\n      try {\n        var _data$user;\n        // First try dedicated avatar upload endpoint\n        response = await httpService.post(\"/auth/users/avatar/\", formData, {\n          headers: {\n            \"Content-Type\": \"multipart/form-data\"\n          }\n        });\n        const {\n          data\n        } = response;\n        console.log(\"Avatar upload response:\", data);\n        avatarUrl = data.avatar || ((_data$user = data.user) === null || _data$user === void 0 ? void 0 : _data$user.avatar) || data.profile_picture;\n      } catch (error) {\n        console.log(\"Avatar endpoint failed, trying profile update:\", error);\n        try {\n          var _data$user2;\n          // Fallback to profile update endpoint\n          response = await httpService.patch(\"/auth/users/me/\", formData, {\n            headers: {\n              \"Content-Type\": \"multipart/form-data\"\n            }\n          });\n          const {\n            data\n          } = response;\n          console.log(\"Profile update response:\", data);\n          avatarUrl = data.avatar || ((_data$user2 = data.user) === null || _data$user2 === void 0 ? void 0 : _data$user2.avatar) || data.profile_picture;\n        } catch (error2) {\n          console.log(\"Profile update endpoint also failed, using local preview:\", error2);\n          // If both endpoints fail, we'll use the local preview\n          // Create a blob URL for the uploaded file\n          avatarUrl = URL.createObjectURL(avatarFile);\n          console.log(\"Created blob URL for avatar:\", avatarUrl);\n        }\n      }\n\n      // If no avatar URL from server, create blob URL for local preview\n      if (!avatarUrl) {\n        avatarUrl = URL.createObjectURL(avatarFile);\n        console.log(\"No avatar URL from server, created blob URL:\", avatarUrl);\n      }\n\n      // Update userInfo with new avatar\n      const updatedUserInfo = {\n        ...userInfo,\n        avatar: avatarUrl\n      };\n      setUserInfo(updatedUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n      console.log(\"Updated userInfo with avatar:\", updatedUserInfo);\n      return avatarUrl;\n    } catch (ex) {\n      var _ex$response6;\n      console.error(\"Upload avatar error:\", ex);\n      console.error(\"Error response:\", (_ex$response6 = ex.response) === null || _ex$response6 === void 0 ? void 0 : _ex$response6.data);\n\n      // As a last resort, create a blob URL\n      try {\n        const blobUrl = URL.createObjectURL(avatarFile);\n        console.log(\"Created fallback blob URL:\", blobUrl);\n        const updatedUserInfo = {\n          ...userInfo,\n          avatar: blobUrl\n        };\n        setUserInfo(updatedUserInfo);\n        localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n        return blobUrl;\n      } catch (blobError) {\n        console.error(\"Failed to create blob URL:\", blobError);\n        throw new Error(\"Upload ảnh thất bại\");\n      }\n    }\n  };\n  const contextData = {\n    authTokens,\n    userInfo,\n    error,\n    login,\n    register,\n    refresh,\n    logout,\n    updateProfile,\n    uploadAvatar\n  };\n  return /*#__PURE__*/_jsxDEV(UserContext.Provider, {\n    value: contextData,\n    children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 19\n    }, this), !loading && children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 364,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProvider, \"ewZig23RdJV2Y6LyZjSnBUkOwZU=\");\n_c = UserProvider;\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");", "map": {"version": 3, "names": ["createContext", "useState", "useEffect", "httpService", "Loader", "jsxDEV", "_jsxDEV", "UserContext", "UserProvider", "_ref", "_s", "children", "authTokens", "setAuthTokens", "localStorage", "getItem", "JSON", "parse", "userInfo", "setUserInfo", "error", "setError", "loading", "setLoading", "login", "username", "password", "data", "post", "access", "refresh", "setItem", "stringify", "setJwt", "userData", "get", "console", "log", "existingUserInfo", "fullUserInfo", "id", "email", "isAdmin", "undefined", "first_name", "last_name", "phone", "gender", "birth_date", "address", "avatar", "profileFetched", "ex", "_ex$response", "response", "register", "logout", "removeItem", "_ex$response2", "_ex$response3", "status", "fetchUserProfile", "updatedUserInfo", "timeInterval", "interval", "setInterval", "clearInterval", "updateProfile", "profileData", "payload", "firstName", "lastName", "birthDate", "patch", "_ex$response4", "_ex$response5", "_ex$response5$data", "message", "uploadAvatar", "avatar<PERSON>ile", "formData", "FormData", "append", "avatarUrl", "_data$user", "headers", "user", "profile_picture", "_data$user2", "error2", "URL", "createObjectURL", "_ex$response6", "blobUrl", "blobError", "Error", "contextData", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/context/userContext.js"], "sourcesContent": ["import { createContext, useState, useEffect } from \"react\";\nimport httpService from \"../services/httpService\";\nimport Loader from \"../components/loader\";\n\nconst UserContext = createContext();\n\nexport default UserContext;\n\nexport const UserProvider = ({ children }) => {\n  const [authTokens, setAuthTokens] = useState(\n    localStorage.getItem(\"authTokens\")\n      ? JSON.parse(localStorage.getItem(\"authTokens\"))\n      : null\n  );\n  const [userInfo, setUserInfo] = useState(\n    localStorage.getItem(\"userInfo\")\n      ? JSON.parse(localStorage.getItem(\"userInfo\"))\n      : null\n  );\n  const [error, setError] = useState(\"\");\n  const [loading, setLoading] = useState(true);\n\n  const login = async (username, password) => {\n    try {\n      const { data } = await httpService.post(\"/auth/jwt/create/\", {\n        username,\n        password,\n      });\n      setAuthTokens({ access: data.access, refresh: data.refresh });\n      localStorage.setItem(\n        \"authTokens\",\n        JSON.stringify({ access: data.access, refresh: data.refresh })\n      );\n\n      // Gọi tiếp API lấy thông tin user đầy đủ\n      httpService.setJwt(data.access); // Đảm bảo token đã set cho request tiếp theo\n      const { data: userData } = await httpService.get(\"/auth/users/me/\");\n\n      console.log(\"Login - User data from server:\", userData);\n\n      // Get existing profile data from localStorage if available\n      const existingUserInfo = localStorage.getItem(\"userInfo\")\n        ? JSON.parse(localStorage.getItem(\"userInfo\"))\n        : {};\n\n      console.log(\n        \"Login - Existing user info from localStorage:\",\n        existingUserInfo\n      );\n\n      const fullUserInfo = {\n        id: userData.id,\n        username: userData.username,\n        email: userData.email,\n        // Use isAdmin from userData (server response) or fallback to JWT data\n        isAdmin: userData.isAdmin !== undefined ? userData.isAdmin : data.isAdmin,\n        // Merge server data with existing localStorage data\n        first_name: userData.first_name || existingUserInfo.first_name || \"\",\n        last_name: userData.last_name || existingUserInfo.last_name || \"\",\n        phone: userData.phone || existingUserInfo.phone || \"\",\n        gender: userData.gender || existingUserInfo.gender || \"\",\n        birth_date: userData.birth_date || existingUserInfo.birth_date || \"\",\n        address: userData.address || existingUserInfo.address || \"\",\n        avatar: userData.avatar || existingUserInfo.avatar || null,\n        profileFetched: true, // Mark as fetched to prevent infinite loop\n      };\n\n      console.log(\"Login - Setting full user info:\", fullUserInfo);\n      console.log(\"Login - isAdmin debug:\", {\n        \"userData.isAdmin\": userData.isAdmin,\n        \"data.isAdmin\": data.isAdmin,\n        \"final isAdmin\": fullUserInfo.isAdmin,\n        \"isAdmin type\": typeof fullUserInfo.isAdmin\n      });\n\n      setUserInfo(fullUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(fullUserInfo));\n      setError(\"\");\n      return true;\n    } catch (ex) {\n      setError({ login: ex.response?.data });\n      return false;\n    }\n  };\n\n  const register = async (username, email, password) => {\n    try {\n      const { data } = await httpService.post(\"/auth/users/\", {\n        username,\n        email,\n        password,\n      });\n      await login(username, password);\n      return true;\n    } catch (ex) {\n      setError({ register: ex.response.data });\n      return false;\n    }\n  };\n\n  const logout = () => {\n    setAuthTokens(null);\n    setUserInfo(null);\n    localStorage.removeItem(\"authTokens\");\n    // Keep userInfo in localStorage for next login - only remove auth tokens\n    // localStorage.removeItem(\"userInfo\"); // Comment this out to preserve profile data\n    console.log(\"Logout - Keeping userInfo in localStorage for next login\");\n    // httpService.setJwt(undefined)\n  };\n\n  const refresh = async () => {\n    try {\n      const { data } = await httpService.post(\"/auth/jwt/refresh/\", {\n        refresh: authTokens.refresh,\n      });\n      // httpService.setJwt(data.access)\n      setAuthTokens({ access: data.access, refresh: data.refresh });\n      localStorage.setItem(\n        \"authTokens\",\n        JSON.stringify({ access: data.access, refresh: data.refresh })\n      );\n    } catch (ex) {\n      // ✅ Chỉ logout khi refresh token thực sự hết hạn\n      console.error(\"Token refresh failed:\", ex.response?.status);\n      if (ex.response?.status === 401) {\n        logout();\n      }\n    }\n  };\n\n  useEffect(() => {\n    if (authTokens) {\n      refresh();\n    }\n    setLoading(false);\n  }, []);\n\n  useEffect(() => {\n    httpService.setJwt(\n      authTokens && authTokens.access ? authTokens.access : null\n    );\n  }, [loading, authTokens]);\n\n  // Separate useEffect for fetching profile to avoid infinite loop\n  useEffect(() => {\n    // If userInfo exists but missing profile fields, fetch from server\n    if (\n      authTokens &&\n      userInfo &&\n      userInfo.id &&\n      (!userInfo.first_name || userInfo.first_name === \"\") &&\n      (!userInfo.last_name || userInfo.last_name === \"\") &&\n      (!userInfo.phone || userInfo.phone === \"\") &&\n      !userInfo.profileFetched // Add flag to prevent infinite loop\n    ) {\n      console.log(\"UserInfo missing profile data, fetching from server...\");\n      fetchUserProfile();\n    }\n  }, [authTokens, userInfo]);\n\n  // Helper function to fetch user profile\n  const fetchUserProfile = async () => {\n    try {\n      const { data: userData } = await httpService.get(\"/auth/users/me/\");\n      console.log(\"Fetched user profile:\", userData);\n\n      const updatedUserInfo = {\n        ...userInfo,\n        first_name: userData.first_name || \"\",\n        last_name: userData.last_name || \"\",\n        phone: userData.phone || \"\",\n        gender: userData.gender || \"\",\n        birth_date: userData.birth_date || \"\",\n        address: userData.address || \"\",\n        avatar: userData.avatar || userInfo?.avatar || null,\n        profileFetched: true, // Add flag to prevent infinite loop\n      };\n\n      console.log(\"Updated user info with profile:\", updatedUserInfo);\n      setUserInfo(updatedUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n    } catch (error) {\n      console.error(\"Failed to fetch user profile:\", error);\n      // Set flag even on error to prevent infinite retry\n      const updatedUserInfo = {\n        ...userInfo,\n        profileFetched: true,\n      };\n      setUserInfo(updatedUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n    }\n  };\n\n  useEffect(() => {\n    let timeInterval = 1000 * 60 * 60; // Refresh tokens after every 1 hour\n    const interval = setInterval(() => {\n      if (authTokens) refresh();\n    }, timeInterval);\n    return () => clearInterval(interval);\n  }, [authTokens]);\n\n  const updateProfile = async (profileData) => {\n    try {\n      console.log(\"Updating profile with data:\", profileData);\n      console.log(\"Current userInfo:\", userInfo);\n\n      let payload = {};\n\n      // Always include profile fields (allow empty strings)\n      payload.first_name = profileData.firstName || \"\";\n      payload.last_name = profileData.lastName || \"\";\n      payload.phone = profileData.phone || \"\";\n      payload.gender = profileData.gender || \"\";\n      payload.birth_date = profileData.birthDate || \"\";\n      payload.address = profileData.address || \"\";\n\n      // Only include username/email/password if they have values and are different\n      if (profileData.username && profileData.username !== userInfo.username) {\n        payload.username = profileData.username;\n      }\n      if (profileData.email && profileData.email !== userInfo.email) {\n        payload.email = profileData.email;\n      }\n      if (profileData.password && profileData.password !== \"\") {\n        payload.password = profileData.password;\n      }\n\n      console.log(\"Sending payload:\", payload);\n\n      const { data } = await httpService.patch(\"/auth/users/me/\", payload);\n      console.log(\"Update response:\", data);\n\n      // Update userInfo with new data - preserve existing data if server doesn't return it\n      const updatedUserInfo = {\n        ...userInfo,\n        username: data.username || userInfo.username,\n        email: data.email || userInfo.email,\n        first_name:\n          data.first_name !== undefined ? data.first_name : payload.first_name,\n        last_name:\n          data.last_name !== undefined ? data.last_name : payload.last_name,\n        phone: data.phone !== undefined ? data.phone : payload.phone,\n        gender: data.gender !== undefined ? data.gender : payload.gender,\n        birth_date:\n          data.birth_date !== undefined ? data.birth_date : payload.birth_date,\n        address: data.address !== undefined ? data.address : payload.address,\n        profileFetched: true, // Mark as having profile data\n      };\n\n      console.log(\"Updated userInfo:\", updatedUserInfo);\n      setUserInfo(updatedUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n      setError(\"\");\n      return true;\n    } catch (ex) {\n      console.error(\"Update profile error:\", ex);\n      console.error(\"Error response:\", ex.response?.data);\n      setError(ex.response?.data?.message || \"Cập nhật thông tin thất bại\");\n      return false;\n    }\n  };\n\n  const uploadAvatar = async (avatarFile) => {\n    try {\n      const formData = new FormData();\n      formData.append(\"avatar\", avatarFile);\n\n      console.log(\"Uploading avatar file:\", avatarFile);\n      console.log(\"FormData:\", formData);\n\n      // Try different endpoints for avatar upload\n      let response;\n      let avatarUrl;\n\n      try {\n        // First try dedicated avatar upload endpoint\n        response = await httpService.post(\"/auth/users/avatar/\", formData, {\n          headers: {\n            \"Content-Type\": \"multipart/form-data\",\n          },\n        });\n        const { data } = response;\n        console.log(\"Avatar upload response:\", data);\n        avatarUrl = data.avatar || data.user?.avatar || data.profile_picture;\n      } catch (error) {\n        console.log(\"Avatar endpoint failed, trying profile update:\", error);\n        try {\n          // Fallback to profile update endpoint\n          response = await httpService.patch(\"/auth/users/me/\", formData, {\n            headers: {\n              \"Content-Type\": \"multipart/form-data\",\n            },\n          });\n          const { data } = response;\n          console.log(\"Profile update response:\", data);\n          avatarUrl = data.avatar || data.user?.avatar || data.profile_picture;\n        } catch (error2) {\n          console.log(\n            \"Profile update endpoint also failed, using local preview:\",\n            error2\n          );\n          // If both endpoints fail, we'll use the local preview\n          // Create a blob URL for the uploaded file\n          avatarUrl = URL.createObjectURL(avatarFile);\n          console.log(\"Created blob URL for avatar:\", avatarUrl);\n        }\n      }\n\n      // If no avatar URL from server, create blob URL for local preview\n      if (!avatarUrl) {\n        avatarUrl = URL.createObjectURL(avatarFile);\n        console.log(\"No avatar URL from server, created blob URL:\", avatarUrl);\n      }\n\n      // Update userInfo with new avatar\n      const updatedUserInfo = {\n        ...userInfo,\n        avatar: avatarUrl,\n      };\n\n      setUserInfo(updatedUserInfo);\n      localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n      console.log(\"Updated userInfo with avatar:\", updatedUserInfo);\n\n      return avatarUrl;\n    } catch (ex) {\n      console.error(\"Upload avatar error:\", ex);\n      console.error(\"Error response:\", ex.response?.data);\n\n      // As a last resort, create a blob URL\n      try {\n        const blobUrl = URL.createObjectURL(avatarFile);\n        console.log(\"Created fallback blob URL:\", blobUrl);\n\n        const updatedUserInfo = {\n          ...userInfo,\n          avatar: blobUrl,\n        };\n\n        setUserInfo(updatedUserInfo);\n        localStorage.setItem(\"userInfo\", JSON.stringify(updatedUserInfo));\n\n        return blobUrl;\n      } catch (blobError) {\n        console.error(\"Failed to create blob URL:\", blobError);\n        throw new Error(\"Upload ảnh thất bại\");\n      }\n    }\n  };\n\n  const contextData = {\n    authTokens,\n    userInfo,\n    error,\n    login,\n    register,\n    refresh,\n    logout,\n    updateProfile,\n    uploadAvatar,\n  };\n\n  return (\n    <UserContext.Provider value={contextData}>\n      {loading && <Loader />}\n      {!loading && children}\n    </UserContext.Provider>\n  );\n};\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,gBAAGP,aAAa,EAAE;AAEnC,eAAeO,WAAW;AAE1B,OAAO,MAAMC,YAAY,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EACvC,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAC1Ca,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAC9BC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,GAC9C,IAAI,CACT;EACD,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CACtCa,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GAC5BC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAC5C,IAAI,CACT;EACD,MAAM,CAACK,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMuB,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAM;QAAEC;MAAK,CAAC,GAAG,MAAMxB,WAAW,CAACyB,IAAI,CAAC,mBAAmB,EAAE;QAC3DH,QAAQ;QACRC;MACF,CAAC,CAAC;MACFb,aAAa,CAAC;QAAEgB,MAAM,EAAEF,IAAI,CAACE,MAAM;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAQ,CAAC,CAAC;MAC7DhB,YAAY,CAACiB,OAAO,CAClB,YAAY,EACZf,IAAI,CAACgB,SAAS,CAAC;QAAEH,MAAM,EAAEF,IAAI,CAACE,MAAM;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAQ,CAAC,CAAC,CAC/D;;MAED;MACA3B,WAAW,CAAC8B,MAAM,CAACN,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC;MACjC,MAAM;QAAEF,IAAI,EAAEO;MAAS,CAAC,GAAG,MAAM/B,WAAW,CAACgC,GAAG,CAAC,iBAAiB,CAAC;MAEnEC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEH,QAAQ,CAAC;;MAEvD;MACA,MAAMI,gBAAgB,GAAGxB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GACrDC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAC5C,CAAC,CAAC;MAENqB,OAAO,CAACC,GAAG,CACT,+CAA+C,EAC/CC,gBAAgB,CACjB;MAED,MAAMC,YAAY,GAAG;QACnBC,EAAE,EAAEN,QAAQ,CAACM,EAAE;QACff,QAAQ,EAAES,QAAQ,CAACT,QAAQ;QAC3BgB,KAAK,EAAEP,QAAQ,CAACO,KAAK;QACrB;QACAC,OAAO,EAAER,QAAQ,CAACQ,OAAO,KAAKC,SAAS,GAAGT,QAAQ,CAACQ,OAAO,GAAGf,IAAI,CAACe,OAAO;QACzE;QACAE,UAAU,EAAEV,QAAQ,CAACU,UAAU,IAAIN,gBAAgB,CAACM,UAAU,IAAI,EAAE;QACpEC,SAAS,EAAEX,QAAQ,CAACW,SAAS,IAAIP,gBAAgB,CAACO,SAAS,IAAI,EAAE;QACjEC,KAAK,EAAEZ,QAAQ,CAACY,KAAK,IAAIR,gBAAgB,CAACQ,KAAK,IAAI,EAAE;QACrDC,MAAM,EAAEb,QAAQ,CAACa,MAAM,IAAIT,gBAAgB,CAACS,MAAM,IAAI,EAAE;QACxDC,UAAU,EAAEd,QAAQ,CAACc,UAAU,IAAIV,gBAAgB,CAACU,UAAU,IAAI,EAAE;QACpEC,OAAO,EAAEf,QAAQ,CAACe,OAAO,IAAIX,gBAAgB,CAACW,OAAO,IAAI,EAAE;QAC3DC,MAAM,EAAEhB,QAAQ,CAACgB,MAAM,IAAIZ,gBAAgB,CAACY,MAAM,IAAI,IAAI;QAC1DC,cAAc,EAAE,IAAI,CAAE;MACxB,CAAC;;MAEDf,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEE,YAAY,CAAC;MAC5DH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QACpC,kBAAkB,EAAEH,QAAQ,CAACQ,OAAO;QACpC,cAAc,EAAEf,IAAI,CAACe,OAAO;QAC5B,eAAe,EAAEH,YAAY,CAACG,OAAO;QACrC,cAAc,EAAE,OAAOH,YAAY,CAACG;MACtC,CAAC,CAAC;MAEFvB,WAAW,CAACoB,YAAY,CAAC;MACzBzB,YAAY,CAACiB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAACO,YAAY,CAAC,CAAC;MAC9DlB,QAAQ,CAAC,EAAE,CAAC;MACZ,OAAO,IAAI;IACb,CAAC,CAAC,OAAO+B,EAAE,EAAE;MAAA,IAAAC,YAAA;MACXhC,QAAQ,CAAC;QAAEG,KAAK,GAAA6B,YAAA,GAAED,EAAE,CAACE,QAAQ,cAAAD,YAAA,uBAAXA,YAAA,CAAa1B;MAAK,CAAC,CAAC;MACtC,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAM4B,QAAQ,GAAG,MAAAA,CAAO9B,QAAQ,EAAEgB,KAAK,EAAEf,QAAQ,KAAK;IACpD,IAAI;MACF,MAAM;QAAEC;MAAK,CAAC,GAAG,MAAMxB,WAAW,CAACyB,IAAI,CAAC,cAAc,EAAE;QACtDH,QAAQ;QACRgB,KAAK;QACLf;MACF,CAAC,CAAC;MACF,MAAMF,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC;MAC/B,OAAO,IAAI;IACb,CAAC,CAAC,OAAO0B,EAAE,EAAE;MACX/B,QAAQ,CAAC;QAAEkC,QAAQ,EAAEH,EAAE,CAACE,QAAQ,CAAC3B;MAAK,CAAC,CAAC;MACxC,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAM6B,MAAM,GAAGA,CAAA,KAAM;IACnB3C,aAAa,CAAC,IAAI,CAAC;IACnBM,WAAW,CAAC,IAAI,CAAC;IACjBL,YAAY,CAAC2C,UAAU,CAAC,YAAY,CAAC;IACrC;IACA;IACArB,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvE;EACF,CAAC;;EAED,MAAMP,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM;QAAEH;MAAK,CAAC,GAAG,MAAMxB,WAAW,CAACyB,IAAI,CAAC,oBAAoB,EAAE;QAC5DE,OAAO,EAAElB,UAAU,CAACkB;MACtB,CAAC,CAAC;MACF;MACAjB,aAAa,CAAC;QAAEgB,MAAM,EAAEF,IAAI,CAACE,MAAM;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAQ,CAAC,CAAC;MAC7DhB,YAAY,CAACiB,OAAO,CAClB,YAAY,EACZf,IAAI,CAACgB,SAAS,CAAC;QAAEH,MAAM,EAAEF,IAAI,CAACE,MAAM;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAQ,CAAC,CAAC,CAC/D;IACH,CAAC,CAAC,OAAOsB,EAAE,EAAE;MAAA,IAAAM,aAAA,EAAAC,aAAA;MACX;MACAvB,OAAO,CAAChB,KAAK,CAAC,uBAAuB,GAAAsC,aAAA,GAAEN,EAAE,CAACE,QAAQ,cAAAI,aAAA,uBAAXA,aAAA,CAAaE,MAAM,CAAC;MAC3D,IAAI,EAAAD,aAAA,GAAAP,EAAE,CAACE,QAAQ,cAAAK,aAAA,uBAAXA,aAAA,CAAaC,MAAM,MAAK,GAAG,EAAE;QAC/BJ,MAAM,EAAE;MACV;IACF;EACF,CAAC;EAEDtD,SAAS,CAAC,MAAM;IACd,IAAIU,UAAU,EAAE;MACdkB,OAAO,EAAE;IACX;IACAP,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAENrB,SAAS,CAAC,MAAM;IACdC,WAAW,CAAC8B,MAAM,CAChBrB,UAAU,IAAIA,UAAU,CAACiB,MAAM,GAAGjB,UAAU,CAACiB,MAAM,GAAG,IAAI,CAC3D;EACH,CAAC,EAAE,CAACP,OAAO,EAAEV,UAAU,CAAC,CAAC;;EAEzB;EACAV,SAAS,CAAC,MAAM;IACd;IACA,IACEU,UAAU,IACVM,QAAQ,IACRA,QAAQ,CAACsB,EAAE,KACV,CAACtB,QAAQ,CAAC0B,UAAU,IAAI1B,QAAQ,CAAC0B,UAAU,KAAK,EAAE,CAAC,KACnD,CAAC1B,QAAQ,CAAC2B,SAAS,IAAI3B,QAAQ,CAAC2B,SAAS,KAAK,EAAE,CAAC,KACjD,CAAC3B,QAAQ,CAAC4B,KAAK,IAAI5B,QAAQ,CAAC4B,KAAK,KAAK,EAAE,CAAC,IAC1C,CAAC5B,QAAQ,CAACiC,cAAc,CAAC;IAAA,EACzB;MACAf,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrEwB,gBAAgB,EAAE;IACpB;EACF,CAAC,EAAE,CAACjD,UAAU,EAAEM,QAAQ,CAAC,CAAC;;EAE1B;EACA,MAAM2C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM;QAAElC,IAAI,EAAEO;MAAS,CAAC,GAAG,MAAM/B,WAAW,CAACgC,GAAG,CAAC,iBAAiB,CAAC;MACnEC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,QAAQ,CAAC;MAE9C,MAAM4B,eAAe,GAAG;QACtB,GAAG5C,QAAQ;QACX0B,UAAU,EAAEV,QAAQ,CAACU,UAAU,IAAI,EAAE;QACrCC,SAAS,EAAEX,QAAQ,CAACW,SAAS,IAAI,EAAE;QACnCC,KAAK,EAAEZ,QAAQ,CAACY,KAAK,IAAI,EAAE;QAC3BC,MAAM,EAAEb,QAAQ,CAACa,MAAM,IAAI,EAAE;QAC7BC,UAAU,EAAEd,QAAQ,CAACc,UAAU,IAAI,EAAE;QACrCC,OAAO,EAAEf,QAAQ,CAACe,OAAO,IAAI,EAAE;QAC/BC,MAAM,EAAEhB,QAAQ,CAACgB,MAAM,KAAIhC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgC,MAAM,KAAI,IAAI;QACnDC,cAAc,EAAE,IAAI,CAAE;MACxB,CAAC;;MAEDf,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEyB,eAAe,CAAC;MAC/D3C,WAAW,CAAC2C,eAAe,CAAC;MAC5BhD,YAAY,CAACiB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAAC8B,eAAe,CAAC,CAAC;IACnE,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACA,MAAM0C,eAAe,GAAG;QACtB,GAAG5C,QAAQ;QACXiC,cAAc,EAAE;MAClB,CAAC;MACDhC,WAAW,CAAC2C,eAAe,CAAC;MAC5BhD,YAAY,CAACiB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAAC8B,eAAe,CAAC,CAAC;IACnE;EACF,CAAC;EAED5D,SAAS,CAAC,MAAM;IACd,IAAI6D,YAAY,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACnC,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAIrD,UAAU,EAAEkB,OAAO,EAAE;IAC3B,CAAC,EAAEiC,YAAY,CAAC;IAChB,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACpD,UAAU,CAAC,CAAC;EAEhB,MAAMuD,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACFhC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+B,WAAW,CAAC;MACvDhC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEnB,QAAQ,CAAC;MAE1C,IAAImD,OAAO,GAAG,CAAC,CAAC;;MAEhB;MACAA,OAAO,CAACzB,UAAU,GAAGwB,WAAW,CAACE,SAAS,IAAI,EAAE;MAChDD,OAAO,CAACxB,SAAS,GAAGuB,WAAW,CAACG,QAAQ,IAAI,EAAE;MAC9CF,OAAO,CAACvB,KAAK,GAAGsB,WAAW,CAACtB,KAAK,IAAI,EAAE;MACvCuB,OAAO,CAACtB,MAAM,GAAGqB,WAAW,CAACrB,MAAM,IAAI,EAAE;MACzCsB,OAAO,CAACrB,UAAU,GAAGoB,WAAW,CAACI,SAAS,IAAI,EAAE;MAChDH,OAAO,CAACpB,OAAO,GAAGmB,WAAW,CAACnB,OAAO,IAAI,EAAE;;MAE3C;MACA,IAAImB,WAAW,CAAC3C,QAAQ,IAAI2C,WAAW,CAAC3C,QAAQ,KAAKP,QAAQ,CAACO,QAAQ,EAAE;QACtE4C,OAAO,CAAC5C,QAAQ,GAAG2C,WAAW,CAAC3C,QAAQ;MACzC;MACA,IAAI2C,WAAW,CAAC3B,KAAK,IAAI2B,WAAW,CAAC3B,KAAK,KAAKvB,QAAQ,CAACuB,KAAK,EAAE;QAC7D4B,OAAO,CAAC5B,KAAK,GAAG2B,WAAW,CAAC3B,KAAK;MACnC;MACA,IAAI2B,WAAW,CAAC1C,QAAQ,IAAI0C,WAAW,CAAC1C,QAAQ,KAAK,EAAE,EAAE;QACvD2C,OAAO,CAAC3C,QAAQ,GAAG0C,WAAW,CAAC1C,QAAQ;MACzC;MAEAU,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgC,OAAO,CAAC;MAExC,MAAM;QAAE1C;MAAK,CAAC,GAAG,MAAMxB,WAAW,CAACsE,KAAK,CAAC,iBAAiB,EAAEJ,OAAO,CAAC;MACpEjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEV,IAAI,CAAC;;MAErC;MACA,MAAMmC,eAAe,GAAG;QACtB,GAAG5C,QAAQ;QACXO,QAAQ,EAAEE,IAAI,CAACF,QAAQ,IAAIP,QAAQ,CAACO,QAAQ;QAC5CgB,KAAK,EAAEd,IAAI,CAACc,KAAK,IAAIvB,QAAQ,CAACuB,KAAK;QACnCG,UAAU,EACRjB,IAAI,CAACiB,UAAU,KAAKD,SAAS,GAAGhB,IAAI,CAACiB,UAAU,GAAGyB,OAAO,CAACzB,UAAU;QACtEC,SAAS,EACPlB,IAAI,CAACkB,SAAS,KAAKF,SAAS,GAAGhB,IAAI,CAACkB,SAAS,GAAGwB,OAAO,CAACxB,SAAS;QACnEC,KAAK,EAAEnB,IAAI,CAACmB,KAAK,KAAKH,SAAS,GAAGhB,IAAI,CAACmB,KAAK,GAAGuB,OAAO,CAACvB,KAAK;QAC5DC,MAAM,EAAEpB,IAAI,CAACoB,MAAM,KAAKJ,SAAS,GAAGhB,IAAI,CAACoB,MAAM,GAAGsB,OAAO,CAACtB,MAAM;QAChEC,UAAU,EACRrB,IAAI,CAACqB,UAAU,KAAKL,SAAS,GAAGhB,IAAI,CAACqB,UAAU,GAAGqB,OAAO,CAACrB,UAAU;QACtEC,OAAO,EAAEtB,IAAI,CAACsB,OAAO,KAAKN,SAAS,GAAGhB,IAAI,CAACsB,OAAO,GAAGoB,OAAO,CAACpB,OAAO;QACpEE,cAAc,EAAE,IAAI,CAAE;MACxB,CAAC;;MAEDf,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEyB,eAAe,CAAC;MACjD3C,WAAW,CAAC2C,eAAe,CAAC;MAC5BhD,YAAY,CAACiB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAAC8B,eAAe,CAAC,CAAC;MACjEzC,QAAQ,CAAC,EAAE,CAAC;MACZ,OAAO,IAAI;IACb,CAAC,CAAC,OAAO+B,EAAE,EAAE;MAAA,IAAAsB,aAAA,EAAAC,aAAA,EAAAC,kBAAA;MACXxC,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEgC,EAAE,CAAC;MAC1ChB,OAAO,CAAChB,KAAK,CAAC,iBAAiB,GAAAsD,aAAA,GAAEtB,EAAE,CAACE,QAAQ,cAAAoB,aAAA,uBAAXA,aAAA,CAAa/C,IAAI,CAAC;MACnDN,QAAQ,CAAC,EAAAsD,aAAA,GAAAvB,EAAE,CAACE,QAAQ,cAAAqB,aAAA,wBAAAC,kBAAA,GAAXD,aAAA,CAAahD,IAAI,cAAAiD,kBAAA,uBAAjBA,kBAAA,CAAmBC,OAAO,KAAI,6BAA6B,CAAC;MACrE,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,UAAU,IAAK;IACzC,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,UAAU,CAAC;MAErC3C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0C,UAAU,CAAC;MACjD3C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2C,QAAQ,CAAC;;MAElC;MACA,IAAI1B,QAAQ;MACZ,IAAI6B,SAAS;MAEb,IAAI;QAAA,IAAAC,UAAA;QACF;QACA9B,QAAQ,GAAG,MAAMnD,WAAW,CAACyB,IAAI,CAAC,qBAAqB,EAAEoD,QAAQ,EAAE;UACjEK,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACF,MAAM;UAAE1D;QAAK,CAAC,GAAG2B,QAAQ;QACzBlB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEV,IAAI,CAAC;QAC5CwD,SAAS,GAAGxD,IAAI,CAACuB,MAAM,MAAAkC,UAAA,GAAIzD,IAAI,CAAC2D,IAAI,cAAAF,UAAA,uBAATA,UAAA,CAAWlC,MAAM,KAAIvB,IAAI,CAAC4D,eAAe;MACtE,CAAC,CAAC,OAAOnE,KAAK,EAAE;QACdgB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEjB,KAAK,CAAC;QACpE,IAAI;UAAA,IAAAoE,WAAA;UACF;UACAlC,QAAQ,GAAG,MAAMnD,WAAW,CAACsE,KAAK,CAAC,iBAAiB,EAAEO,QAAQ,EAAE;YAC9DK,OAAO,EAAE;cACP,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;UACF,MAAM;YAAE1D;UAAK,CAAC,GAAG2B,QAAQ;UACzBlB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEV,IAAI,CAAC;UAC7CwD,SAAS,GAAGxD,IAAI,CAACuB,MAAM,MAAAsC,WAAA,GAAI7D,IAAI,CAAC2D,IAAI,cAAAE,WAAA,uBAATA,WAAA,CAAWtC,MAAM,KAAIvB,IAAI,CAAC4D,eAAe;QACtE,CAAC,CAAC,OAAOE,MAAM,EAAE;UACfrD,OAAO,CAACC,GAAG,CACT,2DAA2D,EAC3DoD,MAAM,CACP;UACD;UACA;UACAN,SAAS,GAAGO,GAAG,CAACC,eAAe,CAACZ,UAAU,CAAC;UAC3C3C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE8C,SAAS,CAAC;QACxD;MACF;;MAEA;MACA,IAAI,CAACA,SAAS,EAAE;QACdA,SAAS,GAAGO,GAAG,CAACC,eAAe,CAACZ,UAAU,CAAC;QAC3C3C,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE8C,SAAS,CAAC;MACxE;;MAEA;MACA,MAAMrB,eAAe,GAAG;QACtB,GAAG5C,QAAQ;QACXgC,MAAM,EAAEiC;MACV,CAAC;MAEDhE,WAAW,CAAC2C,eAAe,CAAC;MAC5BhD,YAAY,CAACiB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAAC8B,eAAe,CAAC,CAAC;MACjE1B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyB,eAAe,CAAC;MAE7D,OAAOqB,SAAS;IAClB,CAAC,CAAC,OAAO/B,EAAE,EAAE;MAAA,IAAAwC,aAAA;MACXxD,OAAO,CAAChB,KAAK,CAAC,sBAAsB,EAAEgC,EAAE,CAAC;MACzChB,OAAO,CAAChB,KAAK,CAAC,iBAAiB,GAAAwE,aAAA,GAAExC,EAAE,CAACE,QAAQ,cAAAsC,aAAA,uBAAXA,aAAA,CAAajE,IAAI,CAAC;;MAEnD;MACA,IAAI;QACF,MAAMkE,OAAO,GAAGH,GAAG,CAACC,eAAe,CAACZ,UAAU,CAAC;QAC/C3C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwD,OAAO,CAAC;QAElD,MAAM/B,eAAe,GAAG;UACtB,GAAG5C,QAAQ;UACXgC,MAAM,EAAE2C;QACV,CAAC;QAED1E,WAAW,CAAC2C,eAAe,CAAC;QAC5BhD,YAAY,CAACiB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAAC8B,eAAe,CAAC,CAAC;QAEjE,OAAO+B,OAAO;MAChB,CAAC,CAAC,OAAOC,SAAS,EAAE;QAClB1D,OAAO,CAAChB,KAAK,CAAC,4BAA4B,EAAE0E,SAAS,CAAC;QACtD,MAAM,IAAIC,KAAK,CAAC,qBAAqB,CAAC;MACxC;IACF;EACF,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBpF,UAAU;IACVM,QAAQ;IACRE,KAAK;IACLI,KAAK;IACL+B,QAAQ;IACRzB,OAAO;IACP0B,MAAM;IACNW,aAAa;IACbW;EACF,CAAC;EAED,oBACExE,OAAA,CAACC,WAAW,CAAC0F,QAAQ;IAACC,KAAK,EAAEF,WAAY;IAAArF,QAAA,GACtCW,OAAO,iBAAIhB,OAAA,CAACF,MAAM;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG,EACrB,CAAChF,OAAO,IAAIX,QAAQ;EAAA;IAAAwF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACA;AAE3B,CAAC;AAAC5F,EAAA,CAxWWF,YAAY;AAAA+F,EAAA,GAAZ/F,YAAY;AAAA,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}