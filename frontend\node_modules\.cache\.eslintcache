[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "37", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "38", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "39", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "40", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "41", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "42", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "43", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "44", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "45", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "46", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "47", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "48", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\utils\\currency.js": "49", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\payboxContext.js": "50", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\PayboxPage.jsx": "51", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminPaybox.jsx": "52", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDeposit.jsx": "53", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxWallet.jsx": "54", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxTransactions.jsx": "55", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositForm.jsx": "56", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDebug.jsx": "57", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxSimpleTest.jsx": "58", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositSimple.jsx": "59", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositTest.jsx": "60", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\UserChat.jsx": "61", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCoupons.jsx": "62", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminChat.jsx": "63", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\couponService.js": "64", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\chat.jsx": "65", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\favoriteContext.js": "66", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\favoritesPage.jsx": "67", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminRefund.jsx": "68", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AISearch.jsx": "69"}, {"size": 649, "mtime": 1750943544495, "results": "70", "hashOfConfig": "71"}, {"size": 362, "mtime": 1750943544502, "results": "72", "hashOfConfig": "71"}, {"size": 6051, "mtime": 1752975557332, "results": "73", "hashOfConfig": "71"}, {"size": 1890, "mtime": 1752592548999, "results": "74", "hashOfConfig": "71"}, {"size": 6833, "mtime": 1752592548998, "results": "75", "hashOfConfig": "71"}, {"size": 12589, "mtime": 1753004228812, "results": "76", "hashOfConfig": "71"}, {"size": 4740, "mtime": 1752975557335, "results": "77", "hashOfConfig": "71"}, {"size": 4693, "mtime": 1752592549010, "results": "78", "hashOfConfig": "71"}, {"size": 4284, "mtime": 1752592548993, "results": "79", "hashOfConfig": "71"}, {"size": 12791, "mtime": 1753010406769, "results": "80", "hashOfConfig": "71"}, {"size": 4681, "mtime": 1752592549007, "results": "81", "hashOfConfig": "71"}, {"size": 29081, "mtime": 1752592549014, "results": "82", "hashOfConfig": "71"}, {"size": 3271, "mtime": 1752592549018, "results": "83", "hashOfConfig": "71"}, {"size": 5028, "mtime": 1752592549016, "results": "84", "hashOfConfig": "71"}, {"size": 43525, "mtime": 1752592549015, "results": "85", "hashOfConfig": "71"}, {"size": 557, "mtime": 1750943544498, "results": "86", "hashOfConfig": "71"}, {"size": 2046, "mtime": 1752592549008, "results": "87", "hashOfConfig": "71"}, {"size": 6374, "mtime": 1752592549013, "results": "88", "hashOfConfig": "71"}, {"size": 5564, "mtime": 1752592549012, "results": "89", "hashOfConfig": "71"}, {"size": 8220, "mtime": 1752592549011, "results": "90", "hashOfConfig": "71"}, {"size": 18058, "mtime": 1752592549017, "results": "91", "hashOfConfig": "71"}, {"size": 392, "mtime": 1750943544489, "results": "92", "hashOfConfig": "71"}, {"size": 988, "mtime": 1750943544487, "results": "93", "hashOfConfig": "71"}, {"size": 1980, "mtime": 1752988928292, "results": "94", "hashOfConfig": "71"}, {"size": 2488, "mtime": 1752592548996, "results": "95", "hashOfConfig": "71"}, {"size": 228, "mtime": 1750943544490, "results": "96", "hashOfConfig": "71"}, {"size": 1499, "mtime": 1752592548995, "results": "97", "hashOfConfig": "71"}, {"size": 737, "mtime": 1750943544487, "results": "98", "hashOfConfig": "71"}, {"size": 956, "mtime": 1751102444680, "results": "99", "hashOfConfig": "71"}, {"size": 4416, "mtime": 1752592548995, "results": "100", "hashOfConfig": "71"}, {"size": 372, "mtime": 1750943544489, "results": "101", "hashOfConfig": "71"}, {"size": 2548, "mtime": 1750943544493, "results": "102", "hashOfConfig": "71"}, {"size": 2512, "mtime": 1751102495516, "results": "103", "hashOfConfig": "71"}, {"size": 1557, "mtime": 1752592548992, "results": "104", "hashOfConfig": "71"}, {"size": 1617, "mtime": 1752592548995, "results": "105", "hashOfConfig": "71"}, {"size": 1826, "mtime": 1750943544491, "results": "106", "hashOfConfig": "71"}, {"size": 27684, "mtime": 1753007015136, "results": "107", "hashOfConfig": "71"}, {"size": 10924, "mtime": 1752592549004, "results": "108", "hashOfConfig": "71"}, {"size": 7625, "mtime": 1751031499765, "results": "109", "hashOfConfig": "71"}, {"size": 445, "mtime": 1750994572623, "results": "110", "hashOfConfig": "71"}, {"size": 3449, "mtime": 1752592548990, "results": "111", "hashOfConfig": "71"}, {"size": 4570, "mtime": 1752975557335, "results": "112", "hashOfConfig": "71"}, {"size": 8263, "mtime": 1751031465839, "results": "113", "hashOfConfig": "71"}, {"size": 703, "mtime": 1753004267495, "results": "114", "hashOfConfig": "71"}, {"size": 8081, "mtime": 1751031022948, "results": "115", "hashOfConfig": "71"}, {"size": 13462, "mtime": 1751035160109, "results": "116", "hashOfConfig": "71"}, {"size": 8445, "mtime": 1751031717046, "results": "117", "hashOfConfig": "71"}, {"size": 702, "mtime": 1752975557334, "results": "118", "hashOfConfig": "71"}, {"size": 1306, "mtime": 1751166353438, "results": "119", "hashOfConfig": "71"}, {"size": 6337, "mtime": 1751167361268, "results": "120", "hashOfConfig": "71"}, {"size": 6392, "mtime": 1751168138189, "results": "121", "hashOfConfig": "71"}, {"size": 11138, "mtime": 1751170467217, "results": "122", "hashOfConfig": "71"}, {"size": 6233, "mtime": 1751162586624, "results": "123", "hashOfConfig": "71"}, {"size": 2507, "mtime": 1751161030218, "results": "124", "hashOfConfig": "71"}, {"size": 5272, "mtime": 1751161099815, "results": "125", "hashOfConfig": "71"}, {"size": 2966, "mtime": 1751161076471, "results": "126", "hashOfConfig": "71"}, {"size": 2706, "mtime": 1751162015083, "results": "127", "hashOfConfig": "71"}, {"size": 3895, "mtime": 1751162852102, "results": "128", "hashOfConfig": "71"}, {"size": 6369, "mtime": 1751163149393, "results": "129", "hashOfConfig": "71"}, {"size": 5358, "mtime": 1751163187519, "results": "130", "hashOfConfig": "71"}, {"size": 1084, "mtime": 1752592549002, "results": "131", "hashOfConfig": "71"}, {"size": 4870, "mtime": 1752592549003, "results": "132", "hashOfConfig": "71"}, {"size": 3111, "mtime": 1752592549003, "results": "133", "hashOfConfig": "71"}, {"size": 355, "mtime": 1752592549018, "results": "134", "hashOfConfig": "71"}, {"size": 3809, "mtime": 1752592548991, "results": "135", "hashOfConfig": "71"}, {"size": 1715, "mtime": 1752592548998, "results": "136", "hashOfConfig": "71"}, {"size": 1550, "mtime": 1752592549008, "results": "137", "hashOfConfig": "71"}, {"size": 6304, "mtime": 1752592549006, "results": "138", "hashOfConfig": "71"}, {"size": 17033, "mtime": 1752975557333, "results": "139", "hashOfConfig": "71"}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, "1xy8mk5", {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "153", "usedDeprecatedRules": "143"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "157", "usedDeprecatedRules": "143"}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "167", "usedDeprecatedRules": "143"}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "171", "usedDeprecatedRules": "143"}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "181", "usedDeprecatedRules": "143"}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "191", "usedDeprecatedRules": "143"}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "195", "usedDeprecatedRules": "143"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "208", "usedDeprecatedRules": "143"}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "212", "usedDeprecatedRules": "143"}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "219"}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "226", "usedDeprecatedRules": "143"}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "219"}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "219"}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "242", "usedDeprecatedRules": "143"}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "252", "usedDeprecatedRules": "143"}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "262", "usedDeprecatedRules": "143"}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "296", "usedDeprecatedRules": "143"}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "300", "usedDeprecatedRules": "143"}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "307", "usedDeprecatedRules": "143"}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "311", "usedDeprecatedRules": "143"}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "315", "usedDeprecatedRules": "143"}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "331", "usedDeprecatedRules": "143"}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "335", "usedDeprecatedRules": "143"}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "143"}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "369", "usedDeprecatedRules": "143"}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["370"], [], "import { createContext, useState } from \"react\";\nimport httpService from \"../services/httpService\";\n\nconst ProductsContext = createContext();\n\nexport default ProductsContext;\n\nexport const ProductsProvider = ({ children }) => {\n  const [productsLoaded, setProductsLoaded] = useState(false);\n  const [products, setProducts] = useState([]);\n  const [error, setError] = useState(\"\");\n  const [brands, setBrands] = useState([]);\n  const [categories, setCategories] = useState([]);\n\n  const loadProducts = async (forced = false) => {\n    if (productsLoaded && !forced) return;\n\n    try {\n      const { data } = await httpService.get(\"/api/products/\");\n      setProducts(data);\n      const { data: brandsData } = await httpService.get(\"/api/brands/\");\n      setBrands(brandsData);\n      const { data: categoriesData } = await httpService.get(\"/api/category/\");\n      setCategories(categoriesData);\n      setError(\"\");\n    } catch (ex) {\n      setError(ex.message);\n    }\n\n    setProductsLoaded(true);\n  };\n\n  const loadProduct = async (id) => {\n    if (productsLoaded) {\n      const product = products.find((p) => p.id == id);\n      return product;\n    }\n\n    try {\n      const { data } = await httpService.get(`/api/products/${id}/`);\n      return data;\n    } catch (ex) {\n      setError(ex.message);\n      return {};\n    }\n  };\n\n  const getProductVariant = async (productId, colorId, sizeId) => {\n    try {\n      const { data } = await httpService.get(`/api/products/${productId}/variants/${colorId}/${sizeId}/`);\n      return data;\n    } catch (ex) {\n      setError(ex.message);\n      return null;\n    }\n  };\n\n  const contextData = {\n    products,\n    error,\n    loadProducts,\n    loadProduct,\n    getProductVariant,\n    // productsLoaded,\n    brands,\n    categories,\n  };\n\n  return (\n    <ProductsContext.Provider value={contextData}>\n      {children}\n    </ProductsContext.Provider>\n  );\n};\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["371"], [], "import { createContext, useState, useContext } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport httpService from \"../services/httpService\";\nimport UserContext from './userContext';\nimport { CURRENCY } from \"../utils/currency\";\n\nconst CartContext = createContext();\n\nexport default CartContext;\n\nexport const CartProvider = ({ children }) => {\n  const [error, setError] = useState(\"\");\n  let [productsInCart, setProductsInCart] = useState(\n    localStorage.getItem(\"cartItems\")\n      ? JSON.parse(localStorage.getItem(\"cartItems\"))\n      : []\n  );\n  const [shippingAddress, setShippingAddress] = useState(\n    localStorage.getItem(\"shippingAddress\")\n      ? JSON.parse(localStorage.getItem(\"shippingAddress\"))\n      : {}\n  );\n  const [paymentMethod, setPaymentMethod] = useState(\n    localStorage.getItem(\"paymentMethod\")\n      ? localStorage.getItem(\"paymentMethod\")\n      : \"Stripe\"\n  );\n  const [couponCode, setCouponCode] = useState(\n    localStorage.getItem(\"couponCode\") || \"\"\n  );\n  const [couponMessage, setCouponMessage] = useState(\"\");\n  const [discountAmount, setDiscountAmount] = useState(0);\n  const navigate = useNavigate();\n  const { logout } = useContext(UserContext);\n\n  const addItemToCart = async (cartItem, qtyParam = 1) => {\n    // cartItem có thể là object {id, qty, variant_id, color, size} hoặc chỉ là id (backward compatibility)\n    let id, qty, variant_id, color, size;\n\n    if (typeof cartItem === 'object') {\n      ({ id, qty, variant_id, color, size } = cartItem);\n    } else {\n      id = cartItem;\n      qty = qtyParam;\n    }\n\n    // Tạo unique key cho sản phẩm (bao gồm cả biến thể)\n    const uniqueKey = variant_id ? `${id}-${variant_id}` : `${id}`;\n    const existingItem = productsInCart.find((prod) => prod.uniqueKey === uniqueKey);\n\n    if (existingItem) {\n      updateItemQty(uniqueKey, existingItem.qty + qty);\n      return;\n    }\n\n    try {\n      const { data } = await httpService.get(`/api/products/${id}/`);\n\n      let productPrice = data.price;\n      let productStock = data.countInStock;\n\n      // Nếu có biến thể, lấy thông tin từ biến thể\n      if (variant_id) {\n        const variant = data.variants?.find(v => v.id === variant_id);\n        if (variant) {\n          productPrice = variant.price;\n          productStock = variant.stock_quantity;\n        }\n      }\n\n      const product = {\n        id: data.id,\n        uniqueKey: uniqueKey,\n        name: data.name,\n        qty: qty,\n        image: data.image,\n        price: productPrice,\n        countInStock: productStock,\n        variant_id: variant_id || null,\n        color: color || null,\n        size: size || null,\n      };\n\n      localStorage.setItem(\n        \"cartItems\",\n        JSON.stringify([...productsInCart, product])\n      );\n      setProductsInCart([...productsInCart, product]);\n    } catch (ex) {\n      setError(ex.message);\n    }\n  };\n\n  const updateItemQty = (uniqueKey, qty) => {\n    const item = productsInCart.find((prod) => prod.uniqueKey === uniqueKey);\n\n    if (!item || item.qty == Number(qty)) return;\n\n    const product = { ...item };\n    product.qty = Number(qty);\n\n    const updatedProductsInCart = productsInCart.map((prod) =>\n      prod.uniqueKey === product.uniqueKey ? product : prod\n    );\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedProductsInCart));\n    setProductsInCart(updatedProductsInCart);\n  };\n\n  const removeFromCart = (uniqueKey) => {\n    const updatedProductsInCart = productsInCart.filter(\n      (prod) => prod.uniqueKey !== uniqueKey\n    );\n\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedProductsInCart));\n    setProductsInCart(updatedProductsInCart);\n  };\n\n  const updateShippingAddress = (address, city, postalCode, country) => {\n    const newShippingAddress = {\n      address,\n      city,\n      postalCode,\n      country,\n    };\n\n    setShippingAddress(newShippingAddress);\n    localStorage.setItem(\"shippingAddress\", JSON.stringify(newShippingAddress));\n  };\n\n  const updatePaymentMethod = (method) => {\n    setPaymentMethod(method);\n    localStorage.setItem(\"paymentMethod\", method);\n  };\n\n  const applyCoupon = async (code) => {\n    try {\n      setCouponMessage(\"Đang kiểm tra mã...\");\n      const { data } = await httpService.post(\"/api/coupons/check/\", {\n        code,\n        total_price: totalPrice,\n      });\n      setCouponMessage(data.message);\n      setDiscountAmount(data.discount_amount);\n      setCouponCode(code);\n      localStorage.setItem(\"couponCode\", code);\n    } catch (ex) {\n      setCouponMessage(ex.response?.data?.error || \"Mã giảm giá không hợp lệ hoặc đã hết hạn.\");\n      setDiscountAmount(0);\n      setCouponCode(\"\");\n      localStorage.removeItem(\"couponCode\");\n    }\n  };\n\n  const totalItemsPrice = Math.round(\n    productsInCart\n      .reduce((acc, prod) => acc + prod.qty * prod.price, 0)\n  );\n  const shippingPrice = totalItemsPrice > CURRENCY.REDUCED_SHIPPING_THRESHOLD ?\n    (totalItemsPrice >= CURRENCY.FREE_SHIPPING_THRESHOLD ? CURRENCY.FREE_SHIPPING : CURRENCY.REDUCED_SHIPPING) :\n    CURRENCY.STANDARD_SHIPPING;\n  const taxPrice = Math.round(0.05 * totalItemsPrice);\n  const totalPrice = totalItemsPrice + shippingPrice + taxPrice;\n\n  const placeOrder = async () => {\n    try {\n      setError(\"\"); // Reset lỗi trước khi gửi\n\n      // Chuẩn bị dữ liệu order items với thông tin biến thể\n      const orderItems = productsInCart.map(item => ({\n        id: item.id,\n        qty: item.qty,\n        variant_id: item.variant_id,\n        color: item.color,\n        size: item.size\n      }));\n\n      const { data } = await httpService.post(\"/api/placeorder/\", {\n        orderItems: orderItems,\n        shippingAddress,\n        paymentMethod,\n        itemsPrice: totalItemsPrice,\n        taxPrice,\n        shippingPrice,\n        totalPrice,\n        coupon_code: couponCode,\n      });\n      setProductsInCart([]);\n      localStorage.removeItem(\"cartItems\");\n      localStorage.removeItem(\"couponCode\");\n      setCouponCode(\"\");\n      setDiscountAmount(0);\n      setCouponMessage(\"\");\n      navigate(`/orders/${data.id}`);\n    } catch (ex) {\n      if (ex.response && ex.response.status === 403) {\n        logout();\n      } else {\n        setError(ex.response?.data?.error || \"Đã xảy ra lỗi khi đặt hàng\");\n      }\n    }\n  };\n\n  const contextData = {\n    error,\n    productsInCart,\n    addItemToCart,\n    updateItemQty,\n    removeFromCart,\n    shippingAddress,\n    updateShippingAddress,\n    paymentMethod,\n    updatePaymentMethod,\n    totalItemsPrice,\n    shippingPrice,\n    taxPrice,\n    totalPrice,\n    placeOrder,\n    couponCode,\n    setCouponCode,\n    couponMessage,\n    discountAmount,\n    applyCoupon,\n  };\n\n  return (\n    <CartContext.Provider value={contextData}>{children}</CartContext.Provider>\n  );\n};", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["372", "373", "374", "375"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["376", "377", "378"], [], "import React, { useState, useContext, useEffect } from \"react\";\nimport { Link, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { <PERSON>, Button, Row, Col } from \"react-bootstrap\";\nimport Message from \"../components/message\";\nimport UserContext from \"../context/userContext\";\nimport \"../styles/loginPage.css\";\n\nfunction LoginPage() {\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const { userInfo, login, error } = useContext(UserContext);\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const redirect = searchParams.get('redirect')\n    ? \"/\" + searchParams.get('redirect')\n    : \"/\";\n\n  useEffect(() => {\n    if (userInfo && userInfo.username) {\n      if (userInfo.isAdmin) {\n        navigate('/admin');\n      } else {\n        navigate(redirect);\n      }\n    }\n  }, [userInfo, navigate, redirect]);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    const status = await login(username, password);\n    if (status) {\n      const userInfo = JSON.parse(localStorage.getItem('userInfo'));\n      if (userInfo && userInfo.isAdmin) {\n        navigate('/admin');\n      } else {\n        navigate(redirect);\n      }\n    }\n  };\n\n  return (\n    <div className=\"login-page\">\n      <div className=\"login-container\">\n        <div className=\"breadcrumb-nav\">\n          <Link to=\"/\">Trang chủ</Link> / <span>Tài khoản</span>\n        </div>\n        \n        <div className=\"auth-tabs\">\n          <div className=\"auth-tab active\">\n            <Link to=\"/login\">ĐĂNG NHẬP</Link>\n          </div>\n          <div className=\"auth-tab\">\n            <Link to={redirect ? `/register?redirect=${redirect}` : \"/register\"}>\n              TẠO TÀI KHOẢN\n            </Link>\n          </div>\n        </div>\n        \n        <div className=\"auth-content\">\n          <div className=\"auth-benefits\">\n            <p>Đăng nhập để truy cập tài khoản của bạn, theo dõi đơn hàng, lưu sản phẩm vào danh sách yêu thích và tận hưởng trải nghiệm mua sắm cá nhân hóa.</p>\n          </div>\n          \n          {error.login && error.login.detail && (\n            <Message variant=\"danger\">\n              <h4>{error.login.detail}</h4>\n            </Message>\n          )}\n          \n          <Form onSubmit={handleSubmit}>\n            <div className=\"form-group\">\n              <label className=\"form-label\" htmlFor=\"username\">Tên tài khoản</label>\n              <input\n                type=\"text\"\n                id=\"username\"\n                className=\"form-control\"\n                placeholder=\"Enter your username\"\n                value={username}\n                onChange={(e) => setUsername(e.target.value)}\n              />\n              {error.login && error.login.username && (\n                <Message variant=\"danger\">{error.login.username}</Message>\n              )}\n            </div>\n            \n            <div className=\"form-group\">\n              <label className=\"form-label\" htmlFor=\"password\">Mật khẩu</label>\n              <input\n                type=\"password\"\n                id=\"password\"\n                className=\"form-control\"\n                placeholder=\"Enter your password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n              {error.login && error.login.password && (\n                <Message variant=\"danger\">{error.login.password}</Message>\n              )}\n            </div>\n            \n            <div className=\"forgot-password\">\n              <Link to=\"/forgot-password\">Quên mật khẩu?</Link>\n            </div>\n            \n            <button type=\"submit\" className=\"auth-button btn-primary\">\n              ĐĂNG NHẬP\n            </button>\n            \n            <div className=\"auth-separator\">\n              <span>HOẶC</span>\n            </div>\n            \n            <div className=\"social-login\">\n              <button type=\"button\" className=\"btn social-btn google\">\n                <i className=\"fab fa-google\"></i> Đăng nhập với Google\n              </button>\n              <button type=\"button\" className=\"btn social-btn facebook\">\n                <i className=\"fab fa-facebook-f\"></i> Đăng nhập với Facebook\n              </button>\n            </div>\n            \n            <div className=\"auth-info\">\n              <p>Bằng việc đăng nhập, bạn đồng ý với <a href=\"/terms\">Điều khoản sử dụng</a> và <a href=\"/privacy\">Chính sách bảo mật</a> của chúng tôi.</p>\n            </div>\n          </Form>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default LoginPage;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["379", "380", "381", "382", "383", "384", "385", "386", "387"], [], "import React from \"react\";\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from \"react-bootstrap\";\nimport { <PERSON><PERSON><PERSON><PERSON>, MDBContainer, MDBRow, MDBCol, MDBIcon } from 'mdb-react-ui-kit';\n\nfunction Footer(props) {\n  return (\n    <MDBFooter bgColor='light' className='text-center text-lg-start text-muted'>\n      <section className='d-flex justify-content-center justify-content-lg-between p-4 border-bottom'>\n        <div className='me-5 d-none d-lg-block'>\n          <span>Get connected with us on social networks:</span>\n        </div>\n\n        <div>\n          <a href='' className='me-4 text-reset'>\n            <MDBIcon fab icon=\"facebook-f\" />\n          </a>\n          <a href='' className='me-4 text-reset'>\n            <MDBIcon fab icon=\"twitter\" />\n          </a>\n          <a href='' className='me-4 text-reset'>\n            <MDBIcon fab icon=\"google\" />\n          </a>\n          <a href='' className='me-4 text-reset'>\n            <MDBIcon fab icon=\"instagram\" />\n          </a>\n          <a href='' className='me-4 text-reset'>\n            <MDBIcon fab icon=\"linkedin\" />\n          </a>\n          <a href='' className='me-4 text-reset'>\n            <MDBIcon fab icon=\"github\" />\n          </a>\n        </div>\n      </section>\n\n      <section className=''>\n        <MDBContainer className='text-center text-md-start mt-5'>\n          <MDBRow className='mt-3'>\n            <MDBCol md=\"3\" lg=\"4\" xl=\"3\" className='mx-auto mb-4'>\n              <h6 className='text-uppercase fw-bold mb-4'>\n                <MDBIcon icon=\"gem\" className=\"me-3\" />\n                TNBHStore\n              </h6>\n              <p>\n                Chào mừng bạn đến với TNB.com — nơi cung cấp những sản phẩm chất lượng với giá tốt nhất. Chúng tôi luôn sẵn sàng phục vụ và hỗ trợ bạn mọi lúc mọi nơi.\n              </p>\n            </MDBCol>\n\n            <MDBCol md=\"2\" lg=\"2\" xl=\"2\" className='mx-auto mb-4'>\n              <h6 className='text-uppercase fw-bold mb-4'>Products</h6>\n              <p>\n                <a href='#!' className='text-reset'>\n                  Angular\n                </a>\n              </p>\n              <p>\n                <a href='#!' className='text-reset'>\n                  React\n                </a>\n              </p>\n              <p>\n                <a href='#!' className='text-reset'>\n                  Vue\n                </a>\n              </p>\n              <p>\n                <a href='#!' className='text-reset'>\n                  Laravel\n                </a>\n              </p>\n            </MDBCol>\n\n            <MDBCol md=\"3\" lg=\"2\" xl=\"2\" className='mx-auto mb-4'>\n              <h6 className='text-uppercase fw-bold mb-4'>Useful links</h6>\n              <p>\n                <a href='#!' className='text-reset'>\n                  Pricing\n                </a>\n              </p>\n              <p>\n                <a href='#!' className='text-reset'>\n                  Settings\n                </a>\n              </p>\n              <p>\n                <a href='#!' className='text-reset'>\n                  Orders\n                </a>\n              </p>\n              <p>\n                <a href='#!' className='text-reset'>\n                  Help\n                </a>\n              </p>\n            </MDBCol>\n\n            <MDBCol md=\"4\" lg=\"3\" xl=\"3\" className='mx-auto mb-md-0 mb-4'>\n              <h6 className='text-uppercase fw-bold mb-4'>Contact</h6>\n              <p>\n                <MDBIcon icon=\"home\" className=\"me-2\" />\n                New York, NY 10012, US\n              </p>\n              <p>\n                <MDBIcon icon=\"envelope\" className=\"me-3\" />\n                <EMAIL>\n              </p>\n              <p>\n                <MDBIcon icon=\"phone\" className=\"me-3\" /> + 01 234 567 88\n              </p>\n              <p>\n                <MDBIcon icon=\"print\" className=\"me-3\" /> + 01 234 567 89\n              </p>\n            </MDBCol>\n          </MDBRow>\n        </MDBContainer>\n      </section>\n\n      <div className='text-center p-4' style={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}>\n        © 2021 Copyright:\n        <a className='text-reset fw-bold' href='https://mdbootstrap.com/'>\n          MDBootstrap.com\n        </a>\n      </div>\n    </MDBFooter>\n  );\n}\n\nexport default Footer;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["388", "389", "390"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["391", "392"], [], "import React, { useEffect, useState, useContext } from \"react\";\nimport { Link, useParams, useNavigate } from \"react-router-dom\";\nimport {\n  Row,\n  Col,\n  Image,\n  Button,\n  Card,\n  Form,\n  Container,\n  Breadcrumb,\n  Tabs,\n  Tab,\n} from \"react-bootstrap\";\nimport Rating from \"../components/rating\";\nimport ProductsContext from \"../context/productsContext\";\nimport Loader from \"../components/loader\";\nimport Message from \"../components/message\";\nimport CartContext from \"../context/cartContext\";\nimport ReviewsList from \"../components/reviewsList\";\nimport { formatVND } from \"../utils/currency\";\nimport { FavoriteContext } from \"../context/favoriteContext\";\nimport UserContext from \"../context/userContext\";\nimport \"../styles/productPage.css\";\nimport { toast } from \"react-toastify\";\n\nfunction ProductPage(props) {\n  const { id } = useParams();\n  const { error, loadProduct, getProductVariant } = useContext(ProductsContext);\n  const { addItemToCart } = useContext(CartContext);\n  const { isFavorite, addToFavorites, removeFromFavorites } =\n    useContext(FavoriteContext);\n  const { userInfo } = useContext(UserContext);\n  const [product, setProduct] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [qty, setQty] = useState(1);\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [selectedColor, setSelectedColor] = useState(\"\");\n  const [selectedSize, setSelectedSize] = useState(\"\");\n  const [selectedVariant, setSelectedVariant] = useState(null);\n  const [currentPrice, setCurrentPrice] = useState(0);\n  const [currentStock, setCurrentStock] = useState(0);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    const fetchData = async () => {\n      const productData = await loadProduct(id);\n      setProduct(productData);\n\n      // Khởi tạo giá và tồn kho\n      if (productData.has_variants) {\n        setCurrentPrice(productData.min_price || productData.price);\n        setCurrentStock(productData.total_stock || 0);\n      } else {\n        setCurrentPrice(productData.price);\n        setCurrentStock(productData.countInStock);\n      }\n\n      setLoading(false);\n      window.scrollTo(0, 0);\n    };\n    fetchData();\n  }, [id, loadProduct]);\n\n  // Function để lấy sizes có sẵn cho màu đã chọn\n  const getAvailableSizesForColor = () => {\n    if (!product.has_variants || !selectedColor || !product.variants) {\n      return product.has_variants ? (product.available_sizes || []).map(size => ({\n        name: size.name,\n        available: true\n      })) : [];\n    }\n\n    // Lấy tất cả sizes và kiểm tra availability\n    const allSizes = (product.available_sizes || []).map(size => {\n      const variantForColorAndSize = product.variants.find(variant =>\n        variant.color.name === selectedColor && variant.size.name === size.name\n      );\n\n      return {\n        name: size.name,\n        available: variantForColorAndSize && variantForColorAndSize.stock_quantity > 0\n      };\n    });\n\n    return allSizes;\n  };\n\n  // Function để lấy colors có sẵn cho size đã chọn\n  const getAvailableColorsForSize = () => {\n    if (!product.has_variants || !selectedSize || !product.variants) {\n      return product.has_variants ? (product.available_colors || []).map(color => ({\n        ...color,\n        available: true\n      })) : [];\n    }\n\n    // Lấy tất cả colors và kiểm tra availability\n    const allColors = (product.available_colors || []).map(color => {\n      const variantForColorAndSize = product.variants.find(variant =>\n        variant.color.name === color.name && variant.size.name === selectedSize\n      );\n\n      return {\n        ...color,\n        available: variantForColorAndSize && variantForColorAndSize.stock_quantity > 0\n      };\n    });\n\n    return allColors;\n  };\n\n  // Effect để reset size khi chọn màu mới (nếu size không có sẵn)\n  useEffect(() => {\n    if (selectedColor && selectedSize) {\n      const availableSizesForNewColor = getAvailableSizesForColor();\n      const sizeStillAvailable = availableSizesForNewColor.find(s => s.name === selectedSize && s.available);\n      if (!sizeStillAvailable) {\n        setSelectedSize(\"\"); // Reset size nếu không có sẵn cho màu mới\n      }\n    }\n  }, [selectedColor]);\n\n  // Effect để reset color khi chọn size mới (nếu color không có sẵn)\n  useEffect(() => {\n    if (selectedSize && selectedColor) {\n      const availableColorsForNewSize = getAvailableColorsForSize();\n      const colorStillAvailable = availableColorsForNewSize.find(c => c.name === selectedColor && c.available);\n      if (!colorStillAvailable) {\n        setSelectedColor(\"\"); // Reset color nếu không có sẵn cho size mới\n      }\n    }\n  }, [selectedSize]);\n\n  // Effect để cập nhật biến thể khi chọn màu sắc và size\n  useEffect(() => {\n    const updateVariant = async () => {\n      if (product.has_variants && selectedColor && selectedSize) {\n        const colorObj = product.available_colors?.find(c => c.name === selectedColor);\n        const sizeObj = product.available_sizes?.find(s => s.name === selectedSize);\n\n        if (colorObj && sizeObj) {\n          const variant = await getProductVariant(product.id, colorObj.id, sizeObj.id);\n          if (variant) {\n            setSelectedVariant(variant);\n            setCurrentPrice(variant.price);\n            setCurrentStock(variant.stock_quantity);\n          } else {\n            // Nếu không tìm thấy biến thể, reset thông tin\n            setSelectedVariant(null);\n            setCurrentPrice(product.min_price || product.price);\n            setCurrentStock(0);\n          }\n        }\n      }\n    };\n\n    updateVariant();\n  }, [selectedColor, selectedSize, product, getProductVariant]);\n\n  const addToCartHandler = () => {\n    const cartItem = {\n      id: Number(id),\n      qty: Number(qty),\n      variant_id: selectedVariant?.id || null,\n      color: selectedColor || null,\n      size: selectedSize || null\n    };\n\n    addItemToCart(cartItem);\n    toast.success(\"Đã thêm vào giỏ hàng!\");\n    // navigate(`/cart`);\n  };\n\n  const handleFavoriteToggle = () => {\n    if (!userInfo) {\n      navigate(\"/login\");\n      return;\n    }\n\n    if (isFavorite(product.id)) {\n      removeFromFavorites(product.id);\n    } else {\n      addToFavorites(product.id);\n    }\n  };\n\n  const handleImageClick = (index) => {\n    setSelectedImage(index);\n  };\n\n  // Giả lập nhiều hình ảnh sản phẩm\n  const productImages = product.image\n    ? [product.image, product.image, product.image, product.image]\n    : [];\n\n  // Lấy dữ liệu màu sắc và kích cỡ từ API với thông tin availability\n  const availableColors = product.has_variants ? getAvailableColorsForSize().map(color => ({\n    name: color.name,\n    value: color.name.toLowerCase(),\n    color: color.hex_code,\n    available: color.available\n  })) : [];\n\n  const availableSizes = getAvailableSizesForColor();\n\n  // FAQ data\n  const faqData = [\n    {\n      question: \"Sản phẩm này có bảo hành không?\",\n      answer:\n        \"Có, sản phẩm được bảo hành 12 tháng từ ngày mua hàng. Bảo hành bao gồm lỗi do nhà sản xuất.\",\n    },\n    {\n      question: \"Thời gian giao hàng là bao lâu?\",\n      answer:\n        \"Thời gian giao hàng từ 2-5 ngày làm việc tùy theo khu vực. Nội thành Hà Nội và TP.HCM giao trong 1-2 ngày.\",\n    },\n    {\n      question: \"Có thể đổi trả sản phẩm không?\",\n      answer:\n        \"Bạn có thể đổi trả sản phẩm trong vòng 7 ngày kể từ ngày nhận hàng với điều kiện sản phẩm còn nguyên vẹn.\",\n    },\n    {\n      question: \"Làm sao để chọn size phù hợp?\",\n      answer:\n        \"Bạn có thể tham khảo bảng size chi tiết trong phần mô tả sản phẩm hoặc liên hệ tư vấn viên để được hỗ trợ.\",\n    },\n    {\n      question: \"Sản phẩm có giống hình ảnh không?\",\n      answer:\n        \"Chúng tôi cam kết hình ảnh sản phẩm 100% thật. Màu sắc có thể chênh lệch nhẹ do màn hình hiển thị.\",\n    },\n  ];\n\n  if (loading) return <Loader />;\n  if (error) return <Message variant=\"danger\">{error}</Message>;\n\n  if (product && product.id)\n    return (\n      <div className=\"product-detail-page\">\n        <Container>\n          {/* Breadcrumb */}\n          <Breadcrumb className=\"product-breadcrumb\">\n            <Breadcrumb.Item linkAs={Link} linkProps={{ to: \"/\" }}>\n              Trang chủ\n            </Breadcrumb.Item>\n            <Breadcrumb.Item linkAs={Link} linkProps={{ to: \"/search\" }}>\n              Sản phẩm\n            </Breadcrumb.Item>\n            <Breadcrumb.Item active>{product.name}</Breadcrumb.Item>\n          </Breadcrumb>\n\n          <Row>\n            {/* Product Images */}\n            <Col lg={5} md={6}>\n              <div className=\"product-images\">\n                <div className=\"main-image\">\n                  <Image\n                    src={productImages[selectedImage] || product.image}\n                    alt={product.name}\n                    fluid\n                  />\n                </div>\n                {productImages.length > 1 && (\n                  <div className=\"thumbnail-images\">\n                    {productImages.map((img, index) => (\n                      <div\n                        key={index}\n                        className={`thumbnail ${\n                          selectedImage === index ? \"active\" : \"\"\n                        }`}\n                        onClick={() => handleImageClick(index)}\n                      >\n                        <Image\n                          src={img}\n                          alt={`${product.name} - ${index + 1}`}\n                          fluid\n                        />\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            </Col>\n\n            {/* Product Info */}\n            <Col lg={7} md={6}>\n              <div className=\"product-info\">\n                <h1 className=\"product-title\">{product.name}</h1>\n\n                <div className=\"product-meta\">\n                  <div className=\"product-rating-wrapper\">\n                    <Rating\n                      value={product.rating}\n                      text={`${product.numReviews} đánh giá`}\n                      color={\"#f8e825\"}\n                    />\n                    <span className=\"rating-value\">\n                      {product.rating\n                        ? Number(product.rating).toFixed(1)\n                        : \"0.0\"}\n                    </span>\n                  </div>\n                  <div className=\"product-sold\">\n                    <i className=\"fas fa-shopping-cart\"></i> Đã bán{\" \"}\n                    {product.total_sold || 0}\n                  </div>\n                </div>\n\n                <div className=\"product-price\">\n                  <span className=\"current-price\">\n                    {formatVND(currentPrice)}\n                  </span>\n                  {product.has_variants && selectedColor && selectedSize && (\n                    <span className=\"variant-info\">\n                      {selectedColor} - {selectedSize}\n                    </span>\n                  )}\n                  {product.oldPrice && (\n                    <>\n                      <span className=\"old-price\">\n                        {formatVND(product.oldPrice)}\n                      </span>\n                      <span className=\"discount-badge\">\n                        -\n                        {Math.round(\n                          ((product.oldPrice - currentPrice) /\n                            product.oldPrice) *\n                            100\n                        )}\n                        %\n                      </span>\n                    </>\n                  )}\n                </div>\n\n                <div className=\"product-description-short\">\n                  <p>\n                    {product.description\n                      ? product.description.substring(0, 150) + \"...\"\n                      : \"Sản phẩm chất lượng cao, được nhiều khách hàng tin tưởng và lựa chọn.\"}\n                  </p>\n                </div>\n\n                {/* Color Selection */}\n                {product.has_variants && (\n                  <div className=\"product-options\">\n                    {availableColors.length > 0 && (\n                      <div className=\"option-group\">\n                        <span className=\"option-label\">Màu sắc:</span>\n                        <div className=\"color-options\">\n                          {availableColors.map((color) => (\n                            <div\n                              key={color.value}\n                              className={`color-option ${\n                                selectedColor === color.name ? \"selected\" : \"\"\n                              } ${!color.available ? \"unavailable\" : \"\"}`}\n                              onClick={() => {\n                                if (color.available) {\n                                  // Nếu click vào màu đã chọn, bỏ chọn\n                                  if (selectedColor === color.name) {\n                                    setSelectedColor(\"\");\n                                  } else {\n                                    setSelectedColor(color.name);\n                                  }\n                                }\n                              }}\n                              title={color.available ? color.name : `${color.name} (Hết hàng)`}\n                              style={{\n                                cursor: color.available ? \"pointer\" : \"not-allowed\",\n                                opacity: color.available ? 1 : 0.5\n                              }}\n                            >\n                              <div\n                                className=\"color-circle\"\n                                style={{\n                                  backgroundColor: color.color,\n                                  border:\n                                    color.name === \"Trắng\"\n                                      ? \"1px solid #ddd\"\n                                      : \"none\",\n                                  opacity: color.available ? 1 : 0.6\n                                }}\n                              ></div>\n                              <span className=\"color-name\">{color.name}</span>\n                              {!color.available && (\n                                <small className=\"text-muted d-block\">(Hết hàng)</small>\n                              )}\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Size Selection */}\n                    {availableSizes.length > 0 && (\n                      <div className=\"option-group\">\n                        <span className=\"option-label\">Kích cỡ:</span>\n                        <div className=\"size-options\">\n                          {availableSizes.map((size) => (\n                            <button\n                              key={size.name}\n                              className={`size-option ${\n                                selectedSize === size.name ? \"selected\" : \"\"\n                              } ${!size.available ? \"unavailable\" : \"\"}`}\n                              onClick={() => {\n                                if (size.available) {\n                                  // Nếu click vào size đã chọn, bỏ chọn\n                                  if (selectedSize === size.name) {\n                                    setSelectedSize(\"\");\n                                  } else {\n                                    setSelectedSize(size.name);\n                                  }\n                                }\n                              }}\n                              disabled={!size.available}\n                              title={size.available ? size.name : `Size ${size.name} (Hết hàng)`}\n                              style={{\n                                opacity: size.available ? 1 : 0.5,\n                                cursor: size.available ? \"pointer\" : \"not-allowed\"\n                              }}\n                            >\n                              {size.name}\n                              {!size.available && (\n                                <small className=\"d-block text-muted\" style={{fontSize: \"0.7rem\"}}>\n                                  (Hết hàng)\n                                </small>\n                              )}\n                            </button>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n\n                <div className=\"product-status\">\n                  <span className=\"status-label\">Trạng thái:</span>\n                  <span\n                    className={`status-value ${\n                      currentStock > 0 ? \"in-stock\" : \"out-of-stock\"\n                    }`}\n                  >\n                    {currentStock > 0 ? \"Còn hàng\" : \"Hết hàng\"}\n                  </span>\n                  {currentStock > 0 && (\n                    <span className=\"stock-count\">\n                      ({currentStock} sản phẩm có sẵn)\n                    </span>\n                  )}\n                  {product.has_variants && (!selectedColor || !selectedSize) && (\n                    <div className=\"variant-warning\">\n                      <small className=\"text-warning\">\n                        Vui lòng chọn màu sắc và kích cỡ để xem tồn kho\n                      </small>\n                    </div>\n                  )}\n                </div>\n\n                {currentStock > 0 && (\n                  <div className=\"product-quantity\">\n                    <span className=\"quantity-label\">Số lượng:</span>\n                    <div className=\"quantity-control\">\n                      <Button\n                        variant=\"outline-secondary\"\n                        className=\"qty-btn\"\n                        onClick={() => qty > 1 && setQty(qty - 1)}\n                      >\n                        <i className=\"fas fa-minus\"></i>\n                      </Button>\n                      <Form.Control\n                        type=\"text\"\n                        value={qty}\n                        onChange={(e) => {\n                          const value = parseInt(e.target.value);\n                          if (\n                            !isNaN(value) &&\n                            value > 0 &&\n                            value <= currentStock\n                          ) {\n                            setQty(value);\n                          }\n                        }}\n                        min=\"1\"\n                        max={currentStock}\n                        readOnly\n                      />\n                      <Button\n                        variant=\"outline-secondary\"\n                        className=\"qty-btn\"\n                        onClick={() =>\n                          qty < currentStock && setQty(qty + 1)\n                        }\n                      >\n                        <i className=\"fas fa-plus\"></i>\n                      </Button>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"product-actions\">\n                  <Button\n                    variant=\"primary\"\n                    className=\"btn-add-to-cart\"\n                    onClick={addToCartHandler}\n                    disabled={\n                      currentStock === 0 ||\n                      (product.has_variants && (!selectedColor || !selectedSize))\n                    }\n                  >\n                    <i className=\"fas fa-shopping-cart\"></i> Thêm vào giỏ hàng\n                  </Button>\n                  <div className=\"product-actions-secondary\">\n                    <Button\n                      variant=\"outline-secondary\"\n                      className=\"btn-buy-now\"\n                      onClick={() => {\n                        addToCartHandler();\n                        navigate(\"/shipping\");\n                      }}\n                      disabled={\n                        currentStock === 0 ||\n                        (product.has_variants && (!selectedColor || !selectedSize))\n                      }\n                    >\n                      Mua ngay\n                    </Button>\n                    <Button\n                      variant=\"outline-danger\"\n                      className=\"btn-favorite\"\n                      onClick={handleFavoriteToggle}\n                    >\n                      <i\n                        className={`${\n                          isFavorite(product.id) ? \"fas\" : \"far\"\n                        } fa-heart`}\n                      ></i>\n                    </Button>\n                  </div>\n                </div>\n\n                <div className=\"product-delivery\">\n                  <div className=\"delivery-info\">\n                    <i className=\"fas fa-truck\"></i>\n                    <div>\n                      <p>Giao hàng miễn phí</p>\n                      <small>Cho đơn hàng từ 300.000đ</small>\n                    </div>\n                  </div>\n                  <div className=\"delivery-info\">\n                    <i className=\"fas fa-undo\"></i>\n                    <div>\n                      <p>Đổi trả dễ dàng</p>\n                      <small>Trong vòng 7 ngày</small>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </Col>\n          </Row>\n\n          <Row className=\"mt-5\">\n            <Col>\n              <Tabs defaultActiveKey=\"description\" className=\"product-tabs\">\n                <Tab eventKey=\"description\" title=\"Thông tin sản phẩm\">\n                  <div className=\"product-description\">\n                    <h4>Mô tả chi tiết</h4>\n                    <p>\n                      {product.description ||\n                        \"Sản phẩm chất lượng cao, được sản xuất từ những nguyên liệu tốt nhất. Thiết kế hiện đại, phù hợp với xu hướng thời trang hiện tại. Đảm bảo độ bền và tính thẩm mỹ cao.\"}\n                    </p>\n\n                    <h5>Thông số kỹ thuật</h5>\n                    <ul>\n                      <li>Chất liệu: Cotton cao cấp</li>\n                      <li>Xuất xứ: Việt Nam</li>\n                      <li>Bảo hành: 12 tháng</li>\n                      <li>Hướng dẫn bảo quản: Giặt máy ở nhiệt độ thường</li>\n                    </ul>\n\n                    <h5>Ưu điểm nổi bật</h5>\n                    <ul>\n                      <li>✓ Chất lượng cao, bền đẹp</li>\n                      <li>✓ Thiết kế hiện đại, thời trang</li>\n                      <li>✓ Giá cả hợp lý</li>\n                      <li>✓ Dịch vụ hậu mãi tốt</li>\n                    </ul>\n                  </div>\n                </Tab>\n                <Tab\n                  eventKey=\"reviews\"\n                  title={`Đánh giá & Nhận xét (${product.numReviews})`}\n                >\n                  <div className=\"product-reviews\">\n                    <div className=\"reviews-summary\">\n                      <div className=\"rating-average\">\n                        <h3>\n                          {product.rating\n                            ? Number(product.rating).toFixed(1)\n                            : \"0.0\"}\n                        </h3>\n                        <Rating\n                          value={product.rating}\n                          text={`${product.numReviews} đánh giá`}\n                          color={\"#f8e825\"}\n                        />\n                        <p className=\"rating-text\">\n                          Trung bình từ {product.numReviews} đánh giá\n                        </p>\n                      </div>\n                      <div className=\"rating-breakdown\">\n                        <div className=\"rating-bar\">\n                          <span>5 sao</span>\n                          <div className=\"bar\">\n                            <div\n                              className=\"fill\"\n                              style={{ width: \"60%\" }}\n                            ></div>\n                          </div>\n                          <span>60%</span>\n                        </div>\n                        <div className=\"rating-bar\">\n                          <span>4 sao</span>\n                          <div className=\"bar\">\n                            <div\n                              className=\"fill\"\n                              style={{ width: \"25%\" }}\n                            ></div>\n                          </div>\n                          <span>25%</span>\n                        </div>\n                        <div className=\"rating-bar\">\n                          <span>3 sao</span>\n                          <div className=\"bar\">\n                            <div\n                              className=\"fill\"\n                              style={{ width: \"10%\" }}\n                            ></div>\n                          </div>\n                          <span>10%</span>\n                        </div>\n                        <div className=\"rating-bar\">\n                          <span>2 sao</span>\n                          <div className=\"bar\">\n                            <div className=\"fill\" style={{ width: \"3%\" }}></div>\n                          </div>\n                          <span>3%</span>\n                        </div>\n                        <div className=\"rating-bar\">\n                          <span>1 sao</span>\n                          <div className=\"bar\">\n                            <div className=\"fill\" style={{ width: \"2%\" }}></div>\n                          </div>\n                          <span>2%</span>\n                        </div>\n                      </div>\n                    </div>\n                    <ReviewsList product={product} />\n                  </div>\n                </Tab>\n                <Tab eventKey=\"faq\" title=\"Câu hỏi thường gặp\">\n                  <div className=\"product-faq\">\n                    <h4>Câu hỏi thường gặp</h4>\n                    <div className=\"faq-list\">\n                      {faqData.map((faq, index) => (\n                        <div key={index} className=\"faq-item\">\n                          <div className=\"faq-question\">\n                            <i className=\"fas fa-question-circle\"></i>\n                            <strong>{faq.question}</strong>\n                          </div>\n                          <div className=\"faq-answer\">\n                            <i className=\"fas fa-check-circle\"></i>\n                            {faq.answer}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                    <div className=\"faq-contact\">\n                      <p>\n                        Không tìm thấy câu trả lời?{\" \"}\n                        <a href=\"/contact\">Liên hệ với chúng tôi</a>\n                      </p>\n                    </div>\n                  </div>\n                </Tab>\n              </Tabs>\n            </Col>\n          </Row>\n\n          {/* Related Products */}\n          <Row className=\"mt-5\">\n            <Col>\n              <h2 className=\"section-title\">Sản phẩm liên quan</h2>\n              <div className=\"related-products\">\n                <Row>\n                  {[...Array(4)].map((_, idx) => (\n                    <Col key={idx} xs={6} md={3}>\n                      <Card className=\"product-card\">\n                        <Link to={`/products/${product.id}`}>\n                          <Card.Img variant=\"top\" src={product.image} />\n                        </Link>\n                        <Card.Body>\n                          <Link\n                            to={`/products/${product.id}`}\n                            className=\"product-name\"\n                          >\n                            {product.name}\n                          </Link>\n                          <div className=\"product-price\">\n                            {formatVND(product.price)}\n                          </div>\n                          <div className=\"product-rating\">\n                            <i className=\"fas fa-star\"></i>{\" \"}\n                            {product.rating || 0}\n                          </div>\n                        </Card.Body>\n                      </Card>\n                    </Col>\n                  ))}\n                </Row>\n              </div>\n            </Col>\n          </Row>\n        </Container>\n      </div>\n    );\n\n  return <Message variant=\"danger\">Không tìm thấy sản phẩm.</Message>;\n}\n\nexport default ProductPage;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["393", "394", "395", "396", "397", "398", "399"], [], "import React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport {\n  <PERSON>,\n  But<PERSON>,\n  Row,\n  Col,\n  Container,\n  Tabs,\n  Tab,\n  Card,\n} from \"react-bootstrap\";\nimport Message from \"../components/message\";\nimport UserContext from \"../context/userContext\";\nimport OrdersList from \"../components/ordersList\";\nimport { FavoriteContext } from \"../context/favoriteContext\";\nimport ProductsContext from \"../context/productsContext\";\nimport { formatVND } from \"../utils/currency\";\nimport \"../styles/profilePage.css\";\n\nfunction ProfilePage(props) {\n  const { userInfo, updateProfile, logout, uploadAvatar } =\n    useContext(UserContext);\n  const {\n    favorites,\n    loading: favoritesLoading,\n    removeFromFavorites,\n  } = useContext(FavoriteContext);\n  const { loadProduct } = useContext(ProductsContext);\n  const [username, setUsername] = useState(\n    userInfo && userInfo.username ? userInfo.username : \"\"\n  );\n  const [email, setEmail] = useState(\n    userInfo && userInfo.email ? userInfo.email : \"\"\n  );\n  const [password, setPassword] = useState(\"\");\n  const [currentPassword, setCurrentPassword] = useState(\"\");\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\n  const [firstName, setFirstName] = useState(\n    userInfo && userInfo.first_name ? userInfo.first_name : \"\"\n  );\n  const [lastName, setLastName] = useState(\n    userInfo && userInfo.last_name ? userInfo.last_name : \"\"\n  );\n  const [phone, setPhone] = useState(\n    userInfo && userInfo.phone ? userInfo.phone : \"\"\n  );\n  const [gender, setGender] = useState(\n    userInfo && userInfo.gender ? userInfo.gender : \"\"\n  );\n  const [birthDate, setBirthDate] = useState(\n    userInfo && userInfo.birth_date ? userInfo.birth_date : \"\"\n  );\n  const [address, setAddress] = useState(\n    userInfo && userInfo.address ? userInfo.address : \"\"\n  );\n  const [avatar, setAvatar] = useState(null);\n  const [avatarPreview, setAvatarPreview] = useState(null);\n  const [activeTab, setActiveTab] = useState(\"personal-info\");\n  const [status, setStatus] = useState({ show: false, success: false });\n  const [isLoading, setIsLoading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [favoriteProducts, setFavoriteProducts] = useState([]);\n  const [loadingFavoriteProducts, setLoadingFavoriteProducts] = useState(false);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (userInfo == null || !userInfo.username) navigate(\"/\");\n  }, []);\n\n  // Update form fields when userInfo changes\n  useEffect(() => {\n    if (userInfo) {\n      console.log(\"Updating form fields with userInfo:\", userInfo);\n      setUsername(userInfo.username || \"\");\n      setEmail(userInfo.email || \"\");\n      setFirstName(userInfo.first_name || \"\");\n      setLastName(userInfo.last_name || \"\");\n      setPhone(userInfo.phone || \"\");\n      setGender(userInfo.gender || \"\");\n      setBirthDate(userInfo.birth_date || \"\");\n      setAddress(userInfo.address || \"\");\n\n      // Set avatar preview - prioritize userInfo.avatar, fallback to sample\n      const currentAvatar =\n        userInfo.avatar && userInfo.avatar !== \"/images/sample.png\"\n          ? userInfo.avatar\n          : \"/images/sample.png\";\n      setAvatarPreview(currentAvatar);\n\n      // Check if user has completed profile info\n      const hasProfileInfo = !!(\n        (userInfo.first_name && userInfo.first_name.trim()) ||\n        (userInfo.last_name && userInfo.last_name.trim()) ||\n        (userInfo.phone && userInfo.phone.trim()) ||\n        (userInfo.address && userInfo.address.trim())\n      );\n      console.log(\"Has profile info:\", hasProfileInfo, {\n        first_name: userInfo.first_name,\n        last_name: userInfo.last_name,\n        phone: userInfo.phone,\n        address: userInfo.address,\n        profileFetched: userInfo.profileFetched,\n        fullUserInfo: userInfo,\n      });\n\n      // Only auto-enter edit mode if no profile info\n      if (!hasProfileInfo) {\n        console.log(\"No profile info found, entering edit mode\");\n        setIsEditMode(true);\n      } else {\n        console.log(\"Profile info exists, staying in view mode\");\n        setIsEditMode(false);\n      }\n    }\n  }, [userInfo]);\n\n  // Check if user has basic profile information\n  const hasProfileInfo =\n    userInfo &&\n    (userInfo.first_name ||\n      userInfo.last_name ||\n      userInfo.phone ||\n      userInfo.address);\n\n  // Load favorite products details\n  useEffect(() => {\n    const loadFavoriteProducts = async () => {\n      if (favorites && favorites.length > 0) {\n        setLoadingFavoriteProducts(true);\n        try {\n          console.log(\"Favorites data:\", favorites); // Debug log\n          const productPromises = favorites.map((fav) => {\n            // Handle both cases: fav.product_id or fav.id\n            const productId = fav.product_id || fav.id;\n            console.log(\"Loading product ID:\", productId); // Debug log\n            return loadProduct(productId);\n          });\n          const products = await Promise.all(productPromises);\n          console.log(\"Loaded products:\", products); // Debug log\n          setFavoriteProducts(products.filter((product) => product)); // Filter out null/undefined\n        } catch (error) {\n          console.error(\"Error loading favorite products:\", error);\n        } finally {\n          setLoadingFavoriteProducts(false);\n        }\n      } else {\n        setFavoriteProducts([]);\n        setLoadingFavoriteProducts(false);\n      }\n    };\n\n    loadFavoriteProducts();\n  }, [favorites, loadProduct]);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsLoading(true);\n    try {\n      const profileData = {\n        username,\n        email,\n        password,\n        firstName,\n        lastName,\n        phone,\n        gender,\n        birthDate,\n        address,\n      };\n\n      console.log(\"Submitting profile data:\", profileData);\n      const updatedStatus = await updateProfile(profileData);\n      console.log(\"Update status:\", updatedStatus);\n\n      setStatus({ show: true, success: updatedStatus });\n      if (updatedStatus) {\n        console.log(\"Update successful, switching to view mode\");\n        // Clear password field after successful update\n        setPassword(\"\");\n        // Switch to view mode after successful update\n        setIsEditMode(false);\n        setTimeout(() => {\n          setStatus({ show: false, success: false });\n        }, 3000);\n      } else {\n        console.log(\"Update failed, staying in edit mode\");\n      }\n    } catch (error) {\n      console.error(\"Profile update error:\", error);\n      setStatus({ show: true, success: false });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleAvatarChange = async (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      console.log(\"Selected file:\", file);\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        alert(\"Kích thước file không được vượt quá 5MB\");\n        return;\n      }\n\n      // Validate file type\n      if (!file.type.startsWith(\"image/\")) {\n        alert(\"Vui lòng chọn file hình ảnh\");\n        return;\n      }\n\n      setAvatar(file);\n      setUploadProgress(10);\n\n      try {\n        // Show preview immediately\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          console.log(\"File read successfully, showing preview\");\n          const previewUrl = e.target.result;\n          setAvatarPreview(previewUrl);\n          console.log(\"Avatar preview set to:\", previewUrl);\n        };\n        reader.readAsDataURL(file);\n\n        setUploadProgress(30);\n\n        // Upload to server\n        console.log(\"Starting upload to server...\");\n        const avatarUrl = await uploadAvatar(file);\n        console.log(\"Upload completed, avatar URL:\", avatarUrl);\n        setUploadProgress(90);\n\n        // Update preview with server URL if available\n        if (avatarUrl) {\n          console.log(\"Updating avatar preview with server URL:\", avatarUrl);\n          setAvatarPreview(avatarUrl);\n        }\n\n        // Always show success message\n        setStatus({\n          show: true,\n          success: true,\n          message: \"Cập nhật ảnh đại diện thành công!\",\n        });\n        setTimeout(() => {\n          setStatus({ show: false, success: false });\n        }, 3000);\n\n        setUploadProgress(100);\n        setTimeout(() => setUploadProgress(0), 1000);\n      } catch (error) {\n        console.error(\"Avatar upload error:\", error);\n        alert(\"Upload ảnh thất bại: \" + error.message);\n        setUploadProgress(0);\n        // Reset to original avatar\n        const originalAvatar =\n          userInfo && userInfo.avatar ? userInfo.avatar : \"/images/sample.png\";\n        console.log(\"Resetting to original avatar:\", originalAvatar);\n        setAvatarPreview(originalAvatar);\n      }\n    }\n\n    // Reset input value to allow selecting the same file again\n    e.target.value = \"\";\n  };\n\n  const handleLogout = () => {\n    logout();\n    navigate(\"/\");\n  };\n\n  const handlePasswordChange = () => {\n    setActiveTab(\"change-password\");\n  };\n\n  const handleChangePassword = async (e) => {\n    e.preventDefault();\n\n    if (!currentPassword) {\n      alert(\"Vui lòng nhập mật khẩu hiện tại\");\n      return;\n    }\n\n    if (!password) {\n      alert(\"Vui lòng nhập mật khẩu mới\");\n      return;\n    }\n\n    if (password !== confirmPassword) {\n      alert(\"Mật khẩu xác nhận không khớp\");\n      return;\n    }\n\n    if (password.length < 6) {\n      alert(\"Mật khẩu mới phải có ít nhất 6 ký tự\");\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      const profileData = {\n        password: password,\n        current_password: currentPassword,\n      };\n\n      const updatedStatus = await updateProfile(profileData);\n      if (updatedStatus) {\n        setStatus({\n          show: true,\n          success: true,\n          message: \"Đổi mật khẩu thành công!\",\n        });\n        // Clear password fields\n        setCurrentPassword(\"\");\n        setPassword(\"\");\n        setConfirmPassword(\"\");\n        setTimeout(() => {\n          setStatus({ show: false, success: false });\n        }, 3000);\n      } else {\n        setStatus({\n          show: true,\n          success: false,\n          message:\n            \"Đổi mật khẩu thất bại. Vui lòng kiểm tra mật khẩu hiện tại!\",\n        });\n      }\n    } catch (error) {\n      console.error(\"Change password error:\", error);\n      setStatus({\n        show: true,\n        success: false,\n        message: \"Đổi mật khẩu thất bại. Vui lòng thử lại!\",\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"profile-page\">\n      <Container className=\"profile-container\">\n        <Row>\n          {/* Profile Sidebar */}\n          <Col lg={3} md={4} className=\"mb-4\">\n            <div className=\"profile-sidebar\">\n              {/* Avatar Section */}\n              <div className=\"profile-avatar-section\">\n                <div\n                  className=\"avatar-upload-btn\"\n                  onClick={() =>\n                    document.getElementById(\"avatar-upload\").click()\n                  }\n                  style={{ cursor: \"pointer\" }}\n                >\n                  <img\n                    src={avatarPreview || \"/images/sample.png\"}\n                    alt=\"Avatar\"\n                    className=\"profile-avatar\"\n                    onError={(e) => {\n                      console.log(\"Avatar load error, falling back to sample\");\n                      e.target.src = \"/image/sample.png\";\n                    }}\n                  />\n                  <input\n                    type=\"file\"\n                    accept=\"image/*\"\n                    onChange={handleAvatarChange}\n                    className=\"avatar-upload-input\"\n                    id=\"avatar-upload\"\n                    style={{ display: \"none\" }}\n                  />\n                </div>\n\n                {uploadProgress > 0 && (\n                  <div className=\"upload-progress\">\n                    <div className=\"progress\">\n                      <div\n                        className=\"progress-bar\"\n                        style={{ width: `${uploadProgress}%` }}\n                      ></div>\n                    </div>\n                    <small className=\"text-muted\">\n                      Đang tải ảnh lên... {Math.round(uploadProgress)}%\n                    </small>\n                  </div>\n                )}\n\n                <h4 className=\"profile-user-name\">\n                  {userInfo?.username || \"Người dùng\"}\n                </h4>\n                <p className=\"profile-user-email\">\n                  {userInfo?.email || \"<EMAIL>\"}\n                </p>\n              </div>\n\n              {/* Profile Stats */}\n              <div className=\"profile-stats\">\n                <div className=\"stat-item\">\n                  <span className=\"stat-number\">0</span>\n                  <div className=\"stat-label\">Đơn hàng</div>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-number\">\n                    {favorites ? favorites.length : 0}\n                  </span>\n                  <div className=\"stat-label\">Yêu thích</div>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-number\">0</span>\n                  <div className=\"stat-label\">Đánh giá</div>\n                </div>\n              </div>\n\n              {/* Navigation Menu */}\n              <ul className=\"profile-nav-menu\">\n                <li className=\"profile-nav-item\">\n                  <a\n                    href=\"#personal-info\"\n                    className={`profile-nav-link ${\n                      activeTab === \"personal-info\" ? \"active\" : \"\"\n                    }`}\n                    onClick={(e) => {\n                      e.preventDefault();\n                      setActiveTab(\"personal-info\");\n                    }}\n                  >\n                    <i className=\"fas fa-user\"></i>\n                    Thông tin cá nhân\n                  </a>\n                </li>\n                <li className=\"profile-nav-item\">\n                  <a\n                    href=\"#change-password\"\n                    className={`profile-nav-link ${\n                      activeTab === \"change-password\" ? \"active\" : \"\"\n                    }`}\n                    onClick={(e) => {\n                      e.preventDefault();\n                      setActiveTab(\"change-password\");\n                    }}\n                  >\n                    <i className=\"fas fa-lock\"></i>\n                    Đổi mật khẩu\n                  </a>\n                </li>\n                <li className=\"profile-nav-item\">\n                  <a\n                    href=\"#orders\"\n                    className={`profile-nav-link ${\n                      activeTab === \"orders\" ? \"active\" : \"\"\n                    }`}\n                    onClick={(e) => {\n                      e.preventDefault();\n                      setActiveTab(\"orders\");\n                    }}\n                  >\n                    <i className=\"fas fa-shopping-bag\"></i>\n                    Đơn hàng của tôi\n                  </a>\n                </li>\n                <li className=\"profile-nav-item\">\n                  <a\n                    href=\"#favorites\"\n                    className={`profile-nav-link ${\n                      activeTab === \"favorites\" ? \"active\" : \"\"\n                    }`}\n                    onClick={(e) => {\n                      e.preventDefault();\n                      setActiveTab(\"favorites\");\n                    }}\n                  >\n                    <i className=\"fas fa-heart\"></i>\n                    Sản phẩm yêu thích\n                  </a>\n                </li>\n                <li className=\"profile-nav-item\">\n                  <a\n                    href=\"#logout\"\n                    className=\"profile-nav-link logout-link\"\n                    onClick={(e) => {\n                      e.preventDefault();\n                      handleLogout();\n                    }}\n                  >\n                    <i className=\"fas fa-sign-out-alt\"></i>\n                    Đăng xuất\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </Col>\n\n          {/* Main Content */}\n          <Col lg={9} md={8}>\n            <div className=\"profile-main-content\">\n              <div className=\"profile-content-header\">\n                <h2 className=\"profile-content-title\">\n                  {activeTab === \"personal-info\" && \"Thông tin cá nhân\"}\n                  {activeTab === \"change-password\" && \"Đổi mật khẩu\"}\n                  {activeTab === \"orders\" && \"Đơn hàng của tôi\"}\n                  {activeTab === \"favorites\" && \"Sản phẩm yêu thích\"}\n                </h2>\n                <p className=\"profile-content-subtitle\">\n                  {activeTab === \"personal-info\" &&\n                    \"Quản lý thông tin hồ sơ để bảo mật tài khoản\"}\n                  {activeTab === \"change-password\" &&\n                    \"Đảm bảo tài khoản của bạn đang sử dụng mật khẩu mạnh\"}\n                  {activeTab === \"orders\" &&\n                    \"Xem và theo dõi tất cả đơn hàng của bạn\"}\n                  {activeTab === \"favorites\" &&\n                    \"Danh sách các sản phẩm bạn đã yêu thích\"}\n                </p>\n              </div>\n\n              <div className=\"profile-tab-content\">\n                {status.show && status.success && (\n                  <div className=\"success-message\">\n                    <i className=\"fas fa-check-circle\"></i>\n                    <span>\n                      {status.message || \"Cập nhật thông tin thành công!\"}\n                    </span>\n                  </div>\n                )}\n\n                {status.show && !status.success && (\n                  <div className=\"error-message\">\n                    <i className=\"fas fa-exclamation-circle\"></i>\n                    <span>Cập nhật thông tin thất bại. Vui lòng thử lại!</span>\n                  </div>\n                )}\n\n                {/* Personal Info Tab */}\n                {activeTab === \"personal-info\" && (\n                  <div className=\"personal-info-section\">\n                    {!isEditMode && hasProfileInfo ? (\n                      // View Mode\n                      <div className=\"profile-view-mode\">\n                        <div className=\"d-flex justify-content-between align-items-center mb-4\">\n                          <h5>Thông tin cá nhân</h5>\n                          <Button\n                            variant=\"outline-primary\"\n                            size=\"sm\"\n                            onClick={() => setIsEditMode(true)}\n                          >\n                            <i className=\"fas fa-edit\"></i> Chỉnh sửa\n                          </Button>\n                        </div>\n\n                        <div className=\"profile-info-grid\">\n                          <div className=\"info-row\">\n                            <div className=\"info-item\">\n                              <label className=\"info-label\">\n                                Tên đăng nhập\n                              </label>\n                              <div className=\"info-value\">\n                                {userInfo?.username || \"Chưa cập nhật\"}\n                              </div>\n                            </div>\n                            <div className=\"info-item\">\n                              <label className=\"info-label\">Email</label>\n                              <div className=\"info-value\">\n                                {userInfo?.email || \"Chưa cập nhật\"}\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"info-row\">\n                            <div className=\"info-item\">\n                              <label className=\"info-label\">Họ</label>\n                              <div className=\"info-value\">\n                                {userInfo?.last_name || \"Chưa cập nhật\"}\n                              </div>\n                            </div>\n                            <div className=\"info-item\">\n                              <label className=\"info-label\">Tên</label>\n                              <div className=\"info-value\">\n                                {userInfo?.first_name || \"Chưa cập nhật\"}\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"info-row\">\n                            <div className=\"info-item\">\n                              <label className=\"info-label\">\n                                Số điện thoại\n                              </label>\n                              <div className=\"info-value\">\n                                {userInfo?.phone || \"Chưa cập nhật\"}\n                              </div>\n                            </div>\n                            <div className=\"info-item\">\n                              <label className=\"info-label\">Giới tính</label>\n                              <div className=\"info-value\">\n                                {userInfo?.gender === \"male\"\n                                  ? \"Nam\"\n                                  : userInfo?.gender === \"female\"\n                                  ? \"Nữ\"\n                                  : userInfo?.gender === \"other\"\n                                  ? \"Khác\"\n                                  : \"Chưa cập nhật\"}\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"info-row\">\n                            <div className=\"info-item\">\n                              <label className=\"info-label\">Ngày sinh</label>\n                              <div className=\"info-value\">\n                                {userInfo?.birth_date\n                                  ? new Date(\n                                      userInfo.birth_date\n                                    ).toLocaleDateString(\"vi-VN\")\n                                  : \"Chưa cập nhật\"}\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"info-row\">\n                            <div className=\"info-item full-width\">\n                              <label className=\"info-label\">Địa chỉ</label>\n                              <div className=\"info-value\">\n                                {userInfo?.address || \"Chưa cập nhật\"}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    ) : (\n                      // Edit Mode\n                      <div className=\"personal-info-form\">\n                        {!hasProfileInfo && (\n                          <div className=\"alert alert-info mb-4\">\n                            <i className=\"fas fa-info-circle\"></i>\n                            <strong> Hoàn thiện thông tin cá nhân</strong>\n                            <p className=\"mb-0 mt-2\">\n                              Vui lòng cập nhật thông tin cá nhân để có trải\n                              nghiệm tốt hơn.\n                            </p>\n                          </div>\n                        )}\n\n                        <div className=\"d-flex justify-content-between align-items-center mb-4\">\n                          <h5>\n                            {hasProfileInfo\n                              ? \"Chỉnh sửa thông tin cá nhân\"\n                              : \"Cập nhật thông tin cá nhân\"}\n                          </h5>\n                          {hasProfileInfo && (\n                            <Button\n                              variant=\"outline-secondary\"\n                              size=\"sm\"\n                              onClick={() => setIsEditMode(false)}\n                            >\n                              <i className=\"fas fa-times\"></i> Hủy\n                            </Button>\n                          )}\n                        </div>\n\n                        <Form onSubmit={handleSubmit}>\n                          <div className=\"form-section\">\n                            <h5 className=\"form-section-title\">\n                              <i className=\"fas fa-user-circle\"></i> Thông tin\n                              tài khoản\n                            </h5>\n                            <Row>\n                              <Col md={6}>\n                                <Form.Group className=\"form-group\">\n                                  <Form.Label className=\"form-label\">\n                                    Tên đăng nhập\n                                  </Form.Label>\n                                  <Form.Control\n                                    type=\"text\"\n                                    placeholder=\"Nhập tên đăng nhập\"\n                                    value={username}\n                                    onChange={(e) =>\n                                      setUsername(e.target.value)\n                                    }\n                                    className=\"form-control\"\n                                  />\n                                </Form.Group>\n                              </Col>\n                              <Col md={6}>\n                                <Form.Group className=\"form-group\">\n                                  <Form.Label className=\"form-label\">\n                                    Email\n                                  </Form.Label>\n                                  <Form.Control\n                                    type=\"email\"\n                                    placeholder=\"Nhập địa chỉ email\"\n                                    value={email}\n                                    onChange={(e) => setEmail(e.target.value)}\n                                    className=\"form-control\"\n                                  />\n                                </Form.Group>\n                              </Col>\n                            </Row>\n                          </div>\n\n                          <div className=\"form-section\">\n                            <h5 className=\"form-section-title\">\n                              <i className=\"fas fa-id-card\"></i> Thông tin cá\n                              nhân\n                            </h5>\n                            <Row>\n                              <Col md={6}>\n                                <Form.Group className=\"form-group\">\n                                  <Form.Label className=\"form-label\">\n                                    Họ\n                                  </Form.Label>\n                                  <Form.Control\n                                    type=\"text\"\n                                    placeholder=\"Nhập họ\"\n                                    value={lastName}\n                                    onChange={(e) =>\n                                      setLastName(e.target.value)\n                                    }\n                                    className=\"form-control\"\n                                  />\n                                </Form.Group>\n                              </Col>\n                              <Col md={6}>\n                                <Form.Group className=\"form-group\">\n                                  <Form.Label className=\"form-label\">\n                                    Tên\n                                  </Form.Label>\n                                  <Form.Control\n                                    type=\"text\"\n                                    placeholder=\"Nhập tên\"\n                                    value={firstName}\n                                    onChange={(e) =>\n                                      setFirstName(e.target.value)\n                                    }\n                                    className=\"form-control\"\n                                  />\n                                </Form.Group>\n                              </Col>\n                            </Row>\n                            <Row>\n                              <Col md={6}>\n                                <Form.Group className=\"form-group\">\n                                  <Form.Label className=\"form-label\">\n                                    Số điện thoại\n                                  </Form.Label>\n                                  <Form.Control\n                                    type=\"tel\"\n                                    placeholder=\"Nhập số điện thoại\"\n                                    value={phone}\n                                    onChange={(e) => setPhone(e.target.value)}\n                                    className=\"form-control\"\n                                  />\n                                </Form.Group>\n                              </Col>\n                              <Col md={6}>\n                                <Form.Group className=\"form-group\">\n                                  <Form.Label className=\"form-label\">\n                                    Giới tính\n                                  </Form.Label>\n                                  <Form.Select\n                                    value={gender}\n                                    onChange={(e) => setGender(e.target.value)}\n                                    className=\"form-select\"\n                                  >\n                                    <option value=\"\">Chọn giới tính</option>\n                                    <option value=\"male\">Nam</option>\n                                    <option value=\"female\">Nữ</option>\n                                    <option value=\"other\">Khác</option>\n                                  </Form.Select>\n                                </Form.Group>\n                              </Col>\n                            </Row>\n                            <Row>\n                              <Col md={6}>\n                                <Form.Group className=\"form-group\">\n                                  <Form.Label className=\"form-label\">\n                                    Ngày sinh\n                                  </Form.Label>\n                                  <Form.Control\n                                    type=\"date\"\n                                    value={birthDate}\n                                    onChange={(e) =>\n                                      setBirthDate(e.target.value)\n                                    }\n                                    className=\"form-control\"\n                                  />\n                                </Form.Group>\n                              </Col>\n                            </Row>\n                            <Form.Group className=\"form-group\">\n                              <Form.Label className=\"form-label\">\n                                Địa chỉ\n                              </Form.Label>\n                              <Form.Control\n                                as=\"textarea\"\n                                rows={3}\n                                placeholder=\"Nhập địa chỉ đầy đủ\"\n                                value={address}\n                                onChange={(e) => setAddress(e.target.value)}\n                                className=\"form-control\"\n                              />\n                            </Form.Group>\n                          </div>\n\n                          <div className=\"text-end\">\n                            <Button\n                              type=\"submit\"\n                              className={`btn-update-profile ${\n                                isLoading ? \"btn-loading\" : \"\"\n                              }`}\n                              disabled={isLoading}\n                            >\n                              {isLoading ? (\n                                <>\n                                  <i className=\"fas fa-spinner fa-spin\"></i>{\" \"}\n                                  Đang cập nhật...\n                                </>\n                              ) : (\n                                <>\n                                  <i className=\"fas fa-save\"></i> Cập nhật thông\n                                  tin\n                                </>\n                              )}\n                            </Button>\n                          </div>\n                        </Form>\n                      </div>\n                    )}\n                  </div>\n                )}\n\n                {/* Change Password Tab */}\n                {activeTab === \"change-password\" && (\n                  <div className=\"personal-info-form\">\n                    <Form onSubmit={handleChangePassword}>\n                      <div className=\"form-section\">\n                        <h5 className=\"form-section-title\">\n                          <i className=\"fas fa-shield-alt\"></i> Đổi mật khẩu\n                        </h5>\n                        {/* <Form.Group className=\"form-group\">\n                          <Form.Label className=\"form-label\">\n                            Mật khẩu hiện tại\n                          </Form.Label>\n                          <Form.Control\n                            type=\"password\"\n                            placeholder=\"Nhập mật khẩu hiện tại\"\n                            value={currentPassword}\n                            onChange={(e) => setCurrentPassword(e.target.value)}\n                            className=\"form-control\"\n                            required\n                          />\n                        </Form.Group> */}\n                        <Form.Group className=\"form-group\">\n                          <Form.Label className=\"form-label\">\n                            Mật khẩu mới\n                          </Form.Label>\n                          <Form.Control\n                            type=\"password\"\n                            placeholder=\"Nhập mật khẩu mới (ít nhất 6 ký tự)\"\n                            value={password}\n                            onChange={(e) => setPassword(e.target.value)}\n                            className=\"form-control\"\n                            minLength={6}\n                            required\n                          />\n                        </Form.Group>\n                        <Form.Group className=\"form-group\">\n                          <Form.Label className=\"form-label\">\n                            Xác nhận mật khẩu mới\n                          </Form.Label>\n                          <Form.Control\n                            type=\"password\"\n                            placeholder=\"Nhập lại mật khẩu mới\"\n                            value={confirmPassword}\n                            onChange={(e) => setConfirmPassword(e.target.value)}\n                            className=\"form-control\"\n                            required\n                          />\n                        </Form.Group>\n                      </div>\n                      <div className=\"text-end\">\n                        <Button\n                          type=\"submit\"\n                          className={`btn-update-profile ${\n                            isLoading ? \"btn-loading\" : \"\"\n                          }`}\n                          disabled={isLoading}\n                        >\n                          {isLoading ? (\n                            <>\n                              <i className=\"fas fa-spinner fa-spin\"></i> Đang\n                              đổi mật khẩu...\n                            </>\n                          ) : (\n                            <>\n                              <i className=\"fas fa-key\"></i> Đổi mật khẩu\n                            </>\n                          )}\n                        </Button>\n                      </div>\n                    </Form>\n                  </div>\n                )}\n\n                {/* Orders Tab */}\n                {activeTab === \"orders\" && (\n                  <div>\n                    <div className=\"d-flex justify-content-between align-items-center mb-4\">\n                      <div>\n                        <h5>Danh sách đơn hàng</h5>\n                        <p className=\"text-muted mb-0\">\n                          Quản lý và theo dõi đơn hàng của bạn\n                        </p>\n                      </div>\n                      <Button variant=\"outline-primary\" size=\"sm\">\n                        <i className=\"fas fa-filter\"></i> Lọc đơn hàng\n                      </Button>\n                    </div>\n\n                    {/* Real Orders List */}\n                    <div className=\"orders-wrapper\">\n                      <OrdersList />\n                    </div>\n                  </div>\n                )}\n\n                {/* Favorites Tab */}\n                {activeTab === \"favorites\" && (\n                  <div>\n                    {loadingFavoriteProducts ? (\n                      <div className=\"text-center py-5\">\n                        <i\n                          className=\"fas fa-spinner fa-spin\"\n                          style={{ fontSize: \"2rem\", color: \"#007bff\" }}\n                        ></i>\n                        <p className=\"mt-3\">Đang tải sản phẩm yêu thích...</p>\n                      </div>\n                    ) : favoriteProducts.length > 0 ? (\n                      <div>\n                        <div className=\"d-flex justify-content-between align-items-center mb-4\">\n                          <div>\n                            <h5>\n                              Sản phẩm yêu thích ({favoriteProducts.length})\n                            </h5>\n                            <p className=\"text-muted mb-0\">\n                              Danh sách các sản phẩm bạn đã yêu thích\n                            </p>\n                          </div>\n                        </div>\n\n                        <Row>\n                          {favoriteProducts.map((product) => (\n                            <Col\n                              key={product.id}\n                              xs={12}\n                              sm={6}\n                              lg={4}\n                              className=\"mb-4\"\n                            >\n                              <Card className=\"favorite-product-card h-100\">\n                                <div className=\"position-relative\">\n                                  <Card.Img\n                                    variant=\"top\"\n                                    src={product.image}\n                                    alt={product.name}\n                                    style={{\n                                      height: \"200px\",\n                                      objectFit: \"cover\",\n                                    }}\n                                  />\n                                  <Button\n                                    variant=\"outline-danger\"\n                                    size=\"sm\"\n                                    className=\"position-absolute top-0 end-0 m-2\"\n                                    onClick={() => {\n                                      console.log(\n                                        \"Removing product from favorites:\",\n                                        product.id\n                                      );\n                                      removeFromFavorites(product.id);\n                                    }}\n                                    title=\"Xóa khỏi yêu thích\"\n                                  >\n                                    <i className=\"fas fa-heart\"></i>\n                                  </Button>\n                                </div>\n                                <Card.Body className=\"d-flex flex-column\">\n                                  <Card.Title\n                                    className=\"h6 mb-2\"\n                                    style={{ minHeight: \"48px\" }}\n                                  >\n                                    {product.name}\n                                  </Card.Title>\n                                  <div className=\"mt-auto\">\n                                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\n                                      <span className=\"text-danger fw-bold fs-5\">\n                                        {formatVND(product.price)}\n                                      </span>\n                                      <div className=\"text-warning\">\n                                        <i className=\"fas fa-star\"></i>{\" \"}\n                                        {product.rating || 0}\n                                      </div>\n                                    </div>\n                                    <div className=\"d-grid gap-2\">\n                                      <Button\n                                        variant=\"primary\"\n                                        size=\"sm\"\n                                        onClick={() =>\n                                          navigate(`/products/${product.id}`)\n                                        }\n                                      >\n                                        <i className=\"fas fa-eye\"></i> Xem chi\n                                        tiết\n                                      </Button>\n                                    </div>\n                                  </div>\n                                </Card.Body>\n                              </Card>\n                            </Col>\n                          ))}\n                        </Row>\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-5\">\n                        <i\n                          className=\"fas fa-heart\"\n                          style={{\n                            fontSize: \"4rem\",\n                            color: \"#ddd\",\n                            marginBottom: \"20px\",\n                          }}\n                        ></i>\n                        <h5>Chưa có sản phẩm yêu thích</h5>\n                        <p className=\"text-muted\">\n                          Hãy thêm những sản phẩm bạn yêu thích để dễ dàng tìm\n                          lại sau này\n                        </p>\n                        <Button\n                          variant=\"primary\"\n                          onClick={() => navigate(\"/search\")}\n                        >\n                          <i className=\"fas fa-shopping-bag\"></i> Khám phá sản\n                          phẩm\n                        </Button>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          </Col>\n        </Row>\n      </Container>\n    </div>\n  );\n}\n\nexport default ProfilePage;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["400"], [], "import React, { useContext, useState, useEffect } from \"react\";\nimport UserContext from \"../context/userContext\";\nimport { useNavigate } from \"react-router-dom\";\nimport Loader from \"../components/loader\";\n\nfunction Logout(props) {\n  const { userInfo, logout } = useContext(UserContext);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (userInfo && userInfo.username) logout();\n    setLoading(false);\n    navigate(\"/login\");\n  }, []);\n\n  if (loading) return <Loader />;\n}\n\nexport default Logout;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["401"], [], "import React, { useContext, useEffect, useState } from \"react\";\nimport { Link, useNavigate, useParams } from \"react-router-dom\";\nimport { Row, Col, ListGroup, Image, Card, Button, Alert, Modal, Form } from \"react-bootstrap\";\nimport httpService from \"../services/httpService\";\nimport UserContext from \"../context/userContext\";\nimport PayboxContext from \"../context/payboxContext\";\nimport Loader from \"../components/loader\";\nimport Message from \"../components/message\";\nimport StripePaymentWrapper from \"../components/stripePaymentWrapper\";\nimport { formatVND } from \"../utils/currency\";\n\nfunction OrderDetailsPage() {\n  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(\"paybox\");\n  const [loading, setLoading] = useState(true);\n  const [orderDetails, setOrderDetails] = useState({});\n  const [refundMessage, setRefundMessage] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const [payboxLoading, setPayboxLoading] = useState(false);\n  const [payboxMessage, setPayboxMessage] = useState(\"\");\n  const [showRefundModal, setShowRefundModal] = useState(false);\n  const [refundReason, setRefundReason] = useState(\"\");\n\n  const { userInfo, logout } = useContext(UserContext);\n  const { wallet, payWithPaybox, hasSufficientBalance, formatVND: formatPayboxVND } = useContext(PayboxContext);\n  const { id } = useParams();\n  const navigate = useNavigate();\n\n  if (!userInfo || !userInfo.username) navigate(\"/login\");\n\n  useEffect(() => {\n    const fetchOrder = async () => {\n      try {\n        const { data } = await httpService.get(`/api/orders/${id}/`);\n        setOrderDetails(data);\n      } catch (ex) {\n        if (ex.response?.status === 403) logout();\n        else setError(ex.response?.data?.error || \"Đã xảy ra lỗi khi tải đơn hàng.\");\n      }\n      setLoading(false);\n    };\n    fetchOrder();\n  }, [id, logout]);\n\n  const handlePayboxPayment = async () => {\n    try {\n      setPayboxLoading(true);\n      setPayboxMessage(\"\");\n      await payWithPaybox(id);\n      const { data } = await httpService.get(`/api/orders/${id}/`);\n      setOrderDetails(data);\n      setPayboxMessage(\"Thanh toán thành công!\");\n    } catch (ex) {\n      setPayboxMessage(ex.response?.data?.error || \"Không thể thanh toán bằng ví Paybox\");\n    } finally {\n      setPayboxLoading(false);\n    }\n  };\n\n  const submitRefundRequest = async () => {\n    if (!refundReason.trim()) {\n      setRefundMessage(\"Vui lòng nhập lý do hoàn tiền!\");\n      return;\n    }\n    try {\n      await httpService.post(\"/api/paybox/refund-request/\", { order_id: orderDetails.id, reason: refundReason });\n      setRefundMessage(\"Yêu cầu hoàn tiền đã được gửi!\");\n      const updated = await httpService.get(`/api/orders/${id}/`);\n      setOrderDetails(updated.data);\n      setShowRefundModal(false);\n      setRefundReason(\"\");\n    } catch (ex) {\n      setRefundMessage(ex.response?.data?.error || \"Không thể gửi yêu cầu hoàn tiền.\");\n    }\n  };\n\n  return (\n    <div className=\"order-details-page\">\n      {loading ? (\n        <Loader />\n      ) : error ? (\n        <Message variant=\"danger\">{error}</Message>\n      ) : (\n        <Row>\n          <Col md={8}>\n            <Card className=\"mb-3 shadow-sm rounded\">\n              <Card.Body>\n                <h3 className=\"mb-3\">Thông tin đơn hàng</h3>\n                <p><strong>Khách:</strong> {orderDetails.user.username}</p>\n                <p><strong>Email:</strong> <a href={`mailto:${orderDetails.user.email}`}>{orderDetails.user.email}</a></p>\n                <p><strong>Địa chỉ:</strong> {orderDetails.shippingAddress.address}, {orderDetails.shippingAddress.city}</p>\n                {orderDetails.isDelivered ? (\n                  <Message variant=\"success\">Đã giao lúc {orderDetails.deliveredAt}</Message>\n                ) : (\n                  <Message variant=\"warning\">Chưa giao</Message>\n                )}\n              </Card.Body>\n            </Card>\n\n            <Card className=\"mb-3 shadow-sm rounded\">\n              <Card.Body>\n                <h3 className=\"mb-3\">Sản phẩm</h3>\n                <ListGroup variant=\"flush\">\n                  {orderDetails.orderItems.map(item => (\n                    <ListGroup.Item key={item.id} className=\"py-3\">\n                      <Row>\n                        <Col md={2}><Image src={item.image} alt={item.productName} fluid rounded /></Col>\n                        <Col>\n                          <Link to={`/product/${item.product}`}>{item.productName}</Link>\n                          {item.variant_info && (\n                            <div className=\"text-muted small mt-1\">\n                              <i className=\"fas fa-tag\"></i> {item.variant_info}\n                            </div>\n                          )}\n                        </Col>\n                        <Col>{item.qty} x {formatVND(item.price)} = <strong>{formatVND(item.qty * item.price)}</strong></Col>\n                      </Row>\n                    </ListGroup.Item>\n                  ))}\n                </ListGroup>\n              </Card.Body>\n            </Card>\n          </Col>\n\n          <Col md={4}>\n            <Card className=\"mb-3 shadow-sm rounded\">\n              <Card.Header><h5>Tóm tắt đơn hàng</h5></Card.Header>\n              <ListGroup variant=\"flush\">\n                <ListGroup.Item>Sản phẩm: {formatVND(orderDetails.itemsPrice)}</ListGroup.Item>\n                <ListGroup.Item>Ship: {formatVND(orderDetails.shippingPrice)}</ListGroup.Item>\n                <ListGroup.Item>Tax: {formatVND(orderDetails.taxPrice)}</ListGroup.Item>\n                <ListGroup.Item><strong>Tổng: {formatVND(orderDetails.totalPrice)}</strong></ListGroup.Item>\n              </ListGroup>\n            </Card>\n\n            {!orderDetails.isPaid && (\n              <>\n                <Card className=\"mb-3 p-3 shadow-sm rounded\">\n                  <Button variant=\"outline-primary\" className=\"mb-2\" onClick={() => setSelectedPaymentMethod(\"paybox\")}>Ví Paybox</Button>\n                  <Button variant=\"outline-primary\" onClick={() => setSelectedPaymentMethod(\"stripe\")}>Thẻ tín dụng</Button>\n                </Card>\n\n                {selectedPaymentMethod === \"paybox\" && (\n                  <Card className=\"mb-3 p-3 shadow-sm rounded\">\n                    <p>Số dư ví: <strong>{formatPayboxVND(wallet.balance)}</strong></p>\n                    {hasSufficientBalance(orderDetails.totalPrice) ? (\n                      <Button variant=\"success\" onClick={handlePayboxPayment} disabled={payboxLoading}>\n                        {payboxLoading ? \"Đang xử lý...\" : `Thanh toán ${formatPayboxVND(orderDetails.totalPrice)}`}\n                      </Button>\n                    ) : (\n                      <Alert variant=\"warning\">Số dư ví không đủ!</Alert>\n                    )}\n                  </Card>\n                )}\n\n                {selectedPaymentMethod === \"stripe\" && (\n                  <StripePaymentWrapper id={orderDetails.id} />\n                )}\n              </>\n            )}\n\n            {orderDetails.isPaid && !orderDetails.isRefunded && !orderDetails.refund_request && (\n              <Button variant=\"outline-danger\" className=\"w-100 mt-3\" onClick={() => setShowRefundModal(true)}>Yêu cầu hoàn tiền</Button>\n            )}\n\n            {refundMessage && <Alert className=\"mt-3\" variant=\"info\">{refundMessage}</Alert>}\n          </Col>\n        </Row>\n      )}\n\n      <Modal show={showRefundModal} onHide={() => setShowRefundModal(false)}>\n        <Modal.Header closeButton><Modal.Title>Yêu cầu hoàn tiền</Modal.Title></Modal.Header>\n        <Modal.Body>\n          <Form>\n            <Form.Group>\n              <Form.Label>Lý do</Form.Label>\n              <Form.Control as=\"textarea\" rows={3} value={refundReason} onChange={(e) => setRefundReason(e.target.value)} />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowRefundModal(false)}>Hủy</Button>\n          <Button variant=\"danger\" onClick={submitRefundRequest}>Gửi</Button>\n        </Modal.Footer>\n      </Modal>\n    </div>\n  );\n}\n\nexport default OrderDetailsPage;", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["402"], [], "import React, { useContext, useState, useEffect } from \"react\";\nimport { Container, Row, Col } from \"react-bootstrap\";\nimport { Link, useNavigate, useSearchParams } from \"react-router-dom\";\nimport ProductsContext from \"../context/productsContext\";\nimport Loader from \"../components/loader\";\nimport Message from \"../components/message\";\nimport AdminRedirect from \"../components/AdminRedirect\";\nimport \"../styles/searchPage.css\";\n\nfunction SearchPage() {\n  const { error, products, loadProducts, brands, categories } =\n    useContext(ProductsContext);\n  const [loading, setLoading] = useState(true);\n  const [errorFilters, setErrorFilters] = useState(\"\");\n  const [selectedBrand, setSelectedBrand] = useState(0);\n  const [selectedCategory, setSelectedCategory] = useState(0);\n  const [priceRange, setPriceRange] = useState(0); // 0 = all prices\n  const [sortBy, setSortBy] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const productsPerPage = 12;\n  \n  const navigate = useNavigate();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const brandParam = searchParams.get(\"brand\")\n    ? Number(searchParams.get(\"brand\"))\n    : 0;\n  const categoryParam = searchParams.get(\"category\")\n    ? Number(searchParams.get(\"category\"))\n    : 0;\n  const priceParam = searchParams.get(\"price\")\n    ? Number(searchParams.get(\"price\"))\n    : 0;\n\n  const keyword = searchParams.get(\"keyword\")\n    ? searchParams.get(\"keyword\")\n    : \"\";\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        await loadProducts();\n        setSelectedBrand(brandParam);\n        setSelectedCategory(categoryParam);\n        setPriceRange(priceParam);\n        setLoading(false);\n      } catch (error) {\n        setErrorFilters(error.message);\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [loadProducts, brandParam, categoryParam, priceParam]);\n\n  // Hàm sắp xếp\n  const sortProducts = (products, sortBy) => {\n    const sortedProducts = [...products];\n    \n    switch (sortBy) {\n      case 'price-asc':\n        return sortedProducts.sort((a, b) => a.price - b.price);\n      case 'price-desc':\n        return sortedProducts.sort((a, b) => b.price - a.price);\n      case 'rating-desc':\n        return sortedProducts.sort((a, b) => (b.rating || 0) - (a.rating || 0));\n      case 'reviews-desc':\n        return sortedProducts.sort((a, b) => (b.numReviews || 0) - (a.numReviews || 0));\n      case 'sold-desc':\n        return sortedProducts.sort((a, b) => (b.total_sold || 0) - (a.total_sold || 0));\n      default:\n        return sortedProducts;\n    }\n  };\n\n  // Hàm lọc theo khoảng giá\n  const filterByPrice = (products, priceRange) => {\n    switch (priceRange) {\n      case 1: // Dưới 100.000đ\n        return products.filter(product => product.price < 100000);\n      case 2: // 100.000đ - 300.000đ\n        return products.filter(product => product.price >= 100000 && product.price <= 300000);\n      case 3: // 300.000đ - 500.000đ\n        return products.filter(product => product.price > 300000 && product.price <= 500000);\n      case 4: // 500.000đ - 1.000.000đ\n        return products.filter(product => product.price > 500000 && product.price <= 1000000);\n      case 5: // Trên 1.000.000đ\n        return products.filter(product => product.price > 1000000);\n      default: // Tất cả giá\n        return products;\n    }\n  };\n\n  let filteredProducts = products.filter((product) =>\n    product.name.toLowerCase().includes(keyword.toLowerCase())\n  );\n\n  if (selectedBrand !== 0) {\n    filteredProducts = filteredProducts.filter(\n      (product) => product.brand === selectedBrand\n    );\n  }\n\n  if (selectedCategory !== 0) {\n    filteredProducts = filteredProducts.filter(\n      (product) => product.category === selectedCategory\n    );\n  }\n\n  // Áp dụng lọc theo khoảng giá\n  filteredProducts = filterByPrice(filteredProducts, priceRange);\n\n  // Áp dụng sắp xếp vào filteredProducts\n  let sortedAndFilteredProducts = sortProducts(filteredProducts, sortBy);\n\n  // Phân trang\n  const indexOfLastProduct = currentPage * productsPerPage;\n  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;\n  const currentProducts = sortedAndFilteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);\n  const totalPages = Math.ceil(sortedAndFilteredProducts.length / productsPerPage);\n\n  // Xử lý chuyển trang\n  const handlePageChange = (pageNumber) => {\n    setCurrentPage(pageNumber);\n    window.scrollTo(0, 0);\n  };\n\n  // Xử lý thay đổi khoảng giá\n  const handlePriceChange = (value) => {\n    setPriceRange(value);\n    setCurrentPage(1); // Reset về trang 1 khi thay đổi bộ lọc\n    \n    // Cập nhật URL với tham số giá mới\n    navigate(`/search?keyword=${keyword}&brand=${selectedBrand}&category=${selectedCategory}&price=${value}`);\n  };\n\n  if (loading) return <Loader />;\n\n  if (error !== \"\" || errorFilters !== \"\")\n    return (\n      <Message variant=\"danger\">\n        <h4>{error !== \"\" ? error : errorFilters}</h4>\n      </Message>\n    );\n\n  // Format giá tiền\n  const formatPrice = (price) => {\n    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);\n  };\n\n  return (\n    <AdminRedirect>\n      <div className=\"search-page\">\n        <Container>\n          <Row className=\"mb-4\">\n            <Col>\n              <nav aria-label=\"breadcrumb\">\n                <ol className=\"breadcrumb\">\n                  <li className=\"breadcrumb-item\"><Link to=\"/\">Home</Link></li>\n                  <li className=\"breadcrumb-item active\">Casual</li>\n                </ol>\n              </nav>\n            </Col>\n          </Row>\n          \n          <Row>\n            <Col md={3}>\n              <div className=\"filter-sidebar\">\n                <div className=\"filter-header\">\n                  <h3>Filters</h3>\n                  <Link\n                    to={`/search?keyword=${keyword}`}\n                    className=\"clear-filters\"\n                    onClick={() => {\n                      setSelectedBrand(0);\n                      setSelectedCategory(0);\n                      setPriceRange(0);\n                      setSortBy('');\n                    }}\n                  >\n                    Clear All\n                  </Link>\n                </div>\n                \n                <div className=\"filter-section\">\n                  <h4>Danh mục</h4>\n                  <div className=\"filter-options\">\n                    <div className=\"filter-option\">\n                      <input \n                        type=\"radio\" \n                        id=\"category-all\" \n                        name=\"category\" \n                        checked={selectedCategory === 0}\n                        onChange={() => {\n                          setSelectedCategory(0);\n                          navigate(`/search?keyword=${keyword}&brand=${selectedBrand}&price=${priceRange}`);\n                        }}\n                      />\n                      <label htmlFor=\"category-all\">Tất cả danh mục</label>\n                    </div>\n                    {categories.map((category) => (\n                      <div className=\"filter-option\" key={category.id}>\n                        <input \n                          type=\"radio\" \n                          id={`category-${category.id}`} \n                          name=\"category\" \n                          checked={selectedCategory === category.id}\n                          onChange={() => {\n                            setSelectedCategory(category.id);\n                            navigate(`/search?keyword=${keyword}&brand=${selectedBrand}&category=${category.id}&price=${priceRange}`);\n                          }}\n                        />\n                        <label htmlFor={`category-${category.id}`}>{category.title}</label>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <div className=\"filter-section\">\n                  <h4>Thương hiệu</h4>\n                  <div className=\"filter-options\">\n                    <div className=\"filter-option\">\n                      <input \n                        type=\"radio\" \n                        id=\"brand-all\" \n                        name=\"brand\" \n                        checked={selectedBrand === 0}\n                        onChange={() => {\n                          setSelectedBrand(0);\n                          navigate(`/search?keyword=${keyword}&category=${selectedCategory}&price=${priceRange}`);\n                        }}\n                      />\n                      <label htmlFor=\"brand-all\">Tất cả thương hiệu</label>\n                    </div>\n                    {brands.map((brand) => (\n                      <div className=\"filter-option\" key={brand.id}>\n                        <input \n                          type=\"radio\" \n                          id={`brand-${brand.id}`} \n                          name=\"brand\" \n                          checked={selectedBrand === brand.id}\n                          onChange={() => {\n                            setSelectedBrand(brand.id);\n                            navigate(`/search?keyword=${keyword}&brand=${brand.id}&category=${selectedCategory}&price=${priceRange}`);\n                          }}\n                        />\n                        <label htmlFor={`brand-${brand.id}`}>{brand.title}</label>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <div className=\"filter-section\">\n                  <h4>Khoảng giá</h4>\n                  <div className=\"filter-options\">\n                    <div className=\"filter-option\">\n                      <input \n                        type=\"radio\" \n                        id=\"price-all\" \n                        name=\"price\" \n                        checked={priceRange === 0}\n                        onChange={() => handlePriceChange(0)}\n                      />\n                      <label htmlFor=\"price-all\">Tất cả giá</label>\n                    </div>\n                    <div className=\"filter-option\">\n                      <input \n                        type=\"radio\" \n                        id=\"price-1\" \n                        name=\"price\" \n                        checked={priceRange === 1}\n                        onChange={() => handlePriceChange(1)}\n                      />\n                      <label htmlFor=\"price-1\">Dưới 100.000₫</label>\n                    </div>\n                    <div className=\"filter-option\">\n                      <input \n                        type=\"radio\" \n                        id=\"price-2\" \n                        name=\"price\" \n                        checked={priceRange === 2}\n                        onChange={() => handlePriceChange(2)}\n                      />\n                      <label htmlFor=\"price-2\">100.000₫ - 300.000₫</label>\n                    </div>\n                    <div className=\"filter-option\">\n                      <input \n                        type=\"radio\" \n                        id=\"price-3\" \n                        name=\"price\" \n                        checked={priceRange === 3}\n                        onChange={() => handlePriceChange(3)}\n                      />\n                      <label htmlFor=\"price-3\">300.000₫ - 500.000₫</label>\n                    </div>\n                    <div className=\"filter-option\">\n                      <input \n                        type=\"radio\" \n                        id=\"price-4\" \n                        name=\"price\" \n                        checked={priceRange === 4}\n                        onChange={() => handlePriceChange(4)}\n                      />\n                      <label htmlFor=\"price-4\">500.000₫ - 1.000.000₫</label>\n                    </div>\n                    <div className=\"filter-option\">\n                      <input \n                        type=\"radio\" \n                        id=\"price-5\" \n                        name=\"price\" \n                        checked={priceRange === 5}\n                        onChange={() => handlePriceChange(5)}\n                      />\n                      <label htmlFor=\"price-5\">Trên 1.000.000₫</label>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </Col>\n            \n            <Col md={9}>\n              <div className=\"sort-container\">\n                <h2 className=\"search-title\">\n                  {keyword ? `Kết quả tìm kiếm cho \"${keyword}\"` : \"Casual\"}\n                </h2>\n                <div className=\"sort-options\">\n                  <span className=\"sort-label\">Sort By: </span>\n                  <select \n                    className=\"sort-select\"\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                  >\n                    <option value=\"\">Most Popular</option>\n                    <option value=\"price-asc\">Price: Low to High</option>\n                    <option value=\"price-desc\">Price: High to Low</option>\n                    <option value=\"rating-desc\">Rating</option>\n                    <option value=\"sold-desc\">Best Selling</option>\n                  </select>\n                </div>\n              </div>\n              \n              <p className=\"search-results-count\">\n                Showing {indexOfFirstProduct + 1}-{Math.min(indexOfLastProduct, sortedAndFilteredProducts.length)} of {sortedAndFilteredProducts.length} products\n              </p>\n              \n              <div className=\"products-grid\">\n                <Row>\n                  {currentProducts.map((product) => (\n                    <Col key={product.id} sm={6} md={6} lg={4} className=\"mb-4\">\n                      <div className=\"product-card\">\n                        <Link to={`/products/${product.id}`} className=\"product-image\">\n                          <img src={product.image} alt={product.name} />\n                          {product.total_sold > 10 && (\n                            <span className=\"product-badge bestseller\">\n                              <i className=\"fas fa-fire-alt mr-1\"></i> Bán chạy\n                            </span>\n                          )}\n                          {product.discount > 0 && (\n                            <span className=\"product-badge discount\">\n                              <i className=\"fas fa-tag mr-1\"></i> -{product.discount}%\n                            </span>\n                          )}\n                          {product.is_new && (\n                            <span className=\"product-badge new\">\n                              <i className=\"fas fa-bolt mr-1\"></i> Mới\n                            </span>\n                          )}\n                        </Link>\n                        <div className=\"product-info\">\n                          <Link to={`/products/${product.id}`} className=\"product-name\">\n                            {product.name}\n                          </Link>\n                          <div className=\"product-meta\">\n                            <div className=\"product-price\">{formatPrice(product.price)}</div>\n                            <div className=\"product-rating\">\n                              <i className=\"fas fa-star\"></i> {product.rating || 0}\n                              <span className=\"product-sold\">Đã bán {product.total_sold || 0}</span>\n                            </div>\n                          </div>\n                          <div className=\"product-actions\">\n                            <Link to={`/products/${product.id}`} className=\"btn-view\">\n                              Xem Chi Tiết\n                            </Link>\n                          </div>\n                        </div>\n                      </div>\n                    </Col>\n                  ))}\n                </Row>\n                \n                {sortedAndFilteredProducts.length === 0 && (\n                  <div className=\"no-products\">\n                    <i className=\"fas fa-search\"></i>\n                    <p>Không tìm thấy sản phẩm phù hợp</p>\n                  </div>\n                )}\n              </div>\n              \n              {sortedAndFilteredProducts.length > 0 && (\n                <nav aria-label=\"Page navigation\">\n                  <ul className=\"pagination\">\n                    <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>\n                      <button \n                        className=\"page-link\" \n                        onClick={() => handlePageChange(currentPage - 1)}\n                        disabled={currentPage === 1}\n                      >\n                        &laquo;\n                      </button>\n                    </li>\n                    \n                    {[...Array(totalPages).keys()].map(number => (\n                      <li \n                        key={number + 1} \n                        className={`page-item ${currentPage === number + 1 ? 'active' : ''}`}\n                      >\n                        <button \n                          className=\"page-link\" \n                          onClick={() => handlePageChange(number + 1)}\n                        >\n                          {number + 1}\n                        </button>\n                      </li>\n                    ))}\n                    \n                    <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>\n                      <button \n                        className=\"page-link\" \n                        onClick={() => handlePageChange(currentPage + 1)}\n                        disabled={currentPage === totalPages}\n                      >\n                        &raquo;\n                      </button>\n                    </li>\n                  </ul>\n                </nav>\n              )}\n            </Col>\n          </Row>\n        </Container>\n      </div>\n    </AdminRedirect>\n  );\n}\n\nexport default SearchPage;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["403", "404"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["405"], [], "// import React, { useEffect, useState } from \"react\";\n// import { Form } from \"react-bootstrap\";\n// import { useNavigate } from 'react-router-dom';\n\n// function SearchBox({ keyword, setKeyword }) {\n//   const [keywordText, setKeywordText] = useState(keyword);\n//   const navigate = useNavigate();\n\n//   const queryParams = new URLSearchParams(window.location.search);\n//   const brandParam = queryParams.get(\"brand\")\n//     ? Number(queryParams.get(\"brand\"))\n//     : 0;\n//   const categoryParam = queryParams.get(\"category\")\n//     ? Number(queryParams.get(\"category\"))\n//     : 0;\n\n//   const handleSubmit = (e) => {\n//     e.preventDefault();\n//     navigate(`/search?keyword=${keywordText}&brand=${brandParam}&category=${categoryParam}`);\n//     setKeyword(keywordText);\n//   };\n\n//   return (\n//     <Form onSubmit={handleSubmit} className=\"d-flex search-form position-relative\">\n//       <Form.Control\n//         type=\"text\"\n//         placeholder=\"Tìm sản phẩm...\"\n//         value={keywordText}\n//         onChange={(e) => setKeywordText(e.currentTarget.value)}\n//         className=\"me-2 search-input-with-icon\"\n//       />\n//       <button type=\"submit\" className=\"search-icon-btn\">\n//         <i className=\"fas fa-search\"></i>\n//       </button>\n//     </Form>\n//   );\n// }\n\n// export default SearchBox;\nimport React, { useEffect, useState } from \"react\";\nimport { useNavigate } from 'react-router-dom';\n\nfunction SearchBox({ keyword, setKeyword }) {\n  const [keywordText, setKeywordText] = useState(keyword);\n  const navigate = useNavigate();\n\n  const queryParams = new URLSearchParams(window.location.search);\n  const brandParam = queryParams.get(\"brand\")\n    ? Number(queryParams.get(\"brand\"))\n    : 0;\n  const categoryParam = queryParams.get(\"category\")\n    ? Number(queryParams.get(\"category\"))\n    : 0;\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    navigate(`/search?keyword=${keywordText}&brand=${brandParam}&category=${categoryParam}`);\n    setKeyword(keywordText);\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"d-flex search-form position-relative\">\n      <input\n        type=\"text\"\n        placeholder=\"Tìm sản phẩm...\"\n        value={keywordText}\n        onChange={(e) => setKeywordText(e.currentTarget.value)}\n        className=\"search-input-with-icon\"\n      />\n      <button type=\"submit\" className=\"search-icon-btn\">\n        <i className=\"fas fa-search\"></i>\n      </button>\n    </form>\n  );\n}\n\nexport default SearchBox;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["406", "407", "408"], [], "import React, { useState, useContext } from \"react\";\nimport { Form, Button, ListGroup, Row, Col, Alert } from \"react-bootstrap\";\nimport Rating from \"./rating\";\nimport UserContext from \"../context/userContext\";\nimport httpService from \"../services/httpService\";\nimport Message from \"./message\";\nimport \"../styles/reviewsList.css\";\n\nfunction ReviewsList({ product }) {\n  const { userInfo } = useContext(UserContext);\n  const [rating, setRating] = useState(0);\n  const [comment, setComment] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [reviews, setReviews] = useState(product.reviews || []);\n\n  const submitHandler = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      const { data } = await httpService.post(`/api/products/${product.id}/reviews/`, {\n        rating,\n        comment,\n      });\n      setReviews([...reviews, data]);\n      setRating(0);\n      setComment(\"\");\n      setSuccess(\"Đánh giá của bạn đã được gửi thành công!\");\n    } catch (ex) {\n      setError(\n        ex.response && ex.response.data.detail\n          ? ex.response.data.detail\n          : \"Đã xảy ra lỗi khi gửi đánh giá\"\n      );\n    }\n    setLoading(false);\n  };\n\n  const formatDate = (dateString) => {\n    const options = { year: 'numeric', month: 'long', day: 'numeric' };\n    return new Date(dateString).toLocaleDateString('vi-VN', options);\n  };\n\n  return (\n    <div className=\"reviews-container\">\n      {reviews.length === 0 ? (\n        <Alert variant=\"info\">Chưa có đánh giá nào cho sản phẩm này</Alert>\n      ) : (\n        <div className=\"reviews-list\">\n          {reviews.map((review) => (\n            <div key={review.id} className=\"review-item\">\n              <div className=\"review-header\">\n                <div className=\"reviewer-info\">\n                  <div className=\"reviewer-avatar\">\n                    {review.name.charAt(0).toUpperCase()}\n                  </div>\n                  <div className=\"reviewer-details\">\n                    <h5>{review.name}</h5>\n                    <p className=\"review-date\">{formatDate(review.createdAt)}</p>\n                  </div>\n                </div>\n                <div className=\"review-rating\">\n                  <Rating value={review.rating} color=\"#f8e825\" />\n                </div>\n              </div>\n              <div className=\"review-content\">\n                <p>{review.comment}</p>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      <div className=\"review-form-container\">\n        <h4>Viết đánh giá</h4>\n        {success && <Message variant=\"success\">{success}</Message>}\n        {error && <Message variant=\"danger\">{error}</Message>}\n        \n        {userInfo ? (\n          <Form onSubmit={submitHandler}>\n            <Form.Group controlId=\"rating\" className=\"mb-3\">\n              <Form.Label>Đánh giá</Form.Label>\n              <div className=\"rating-select\">\n                {[1, 2, 3, 4, 5].map((star) => (\n                  <div \n                    key={star} \n                    className={`rating-star ${rating >= star ? 'active' : ''}`}\n                    onClick={() => setRating(star)}\n                  >\n                    <i className=\"fas fa-star\"></i>\n                  </div>\n                ))}\n              </div>\n            </Form.Group>\n\n            <Form.Group controlId=\"comment\" className=\"mb-3\">\n              <Form.Label>Bình luận</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                value={comment}\n                onChange={(e) => setComment(e.target.value)}\n                placeholder=\"Chia sẻ trải nghiệm của bạn về sản phẩm này\"\n              ></Form.Control>\n            </Form.Group>\n\n            <Button\n              type=\"submit\"\n              variant=\"primary\"\n              disabled={loading}\n              className=\"submit-review-btn\"\n            >\n              {loading ? \"Đang gửi...\" : \"Gửi đánh giá\"}\n            </Button>\n          </Form>\n        ) : (\n          <Message variant=\"info\">\n            Vui lòng <a href=\"/login\">đăng nhập</a> để viết đánh giá\n          </Message>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default ReviewsList;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["409"], [], "import React, { useContext, useState, useEffect } from \"react\";\nimport httpService from \"../services/httpService\";\nimport UserContext from \"../context/userContext\";\nimport Loader from \"./loader\";\nimport Message from \"./message\";\nimport { Button, Table } from \"react-bootstrap\";\nimport { LinkContainer } from 'react-router-bootstrap';\nimport { formatVND } from \"../utils/currency\";\n\nfunction OrdersList(props) {\n  const [orders, setOrders] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const { logout } = useContext(UserContext);\n\n  useEffect(() => {\n    const fecthOrders = async () => {\n      try {\n        const { data } = await httpService.get(\"/api/orders/\");\n        setOrders(data);\n      } catch (ex) {\n        if (ex.response && ex.response.status == 403) logout();\n        setError(ex.message);\n      }\n      setLoading(false);\n    };\n    fecthOrders();\n  });\n\n  return (\n    <div>\n      {loading ? (\n        <Loader />\n      ) : error ? (\n        <Message variant=\"danger\">{error}</Message>\n      ) : (\n        <Table striped responsive className=\"table-sm\">\n          <thead>\n            <tr>\n              <th>ID</th>\n              <th>Date</th>\n              <th>Total</th>\n              <th>Paid</th>\n              <th>Delivered</th>\n              <th></th>\n            </tr>\n          </thead>\n\n          <tbody>\n            {orders.map((order) => (\n              <tr key={order.id}>\n                <td>{order.id}</td>\n                <td>{order.createdAt.substring(0, 10)}</td>\n                <td>{formatVND(order.totalPrice)}</td>\n                <td>\n                  {order.isPaid && order.paidAt ? (\n                    order.paidAt.substring(0, 10)\n                  ) : (\n                    <i className=\"fas fa-times\" style={{ color: \"red\" }}></i>\n                  )}\n                </td>\n                <td>\n                  {order.isDelivered && order.deliveredAt ? (\n                    order.deliveredAt.substring(0, 10)\n                  ) : (\n                    <i className=\"fas fa-times\" style={{ color: \"red\" }}></i>\n                  )}\n                </td>\n                <td>\n                    <LinkContainer to={`/orders/${order.id}/`}>\n                        <Button className=\"btn-sm btn-light\">Details</Button>\n                    </LinkContainer>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </Table>\n      )}\n    </div>\n  );\n}\n\nexport default OrdersList;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["410", "411", "412"], [], "import React, { useEffect, useState } from \"react\";\nimport {\n  PaymentElement,\n  LinkAuthenticationElement,\n  useStripe,\n  useElements,\n} from \"@stripe/react-stripe-js\";\nimport httpService from \"../services/httpService\";\n\nexport default function PaymentForm({ id }) {\n  const stripe = useStripe();\n  const elements = useElements();\n\n  const [email, setEmail] = useState(\"\");\n  const [message, setMessage] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  let redirectURL = `${window.location.href.split('#')[0] + '#'}/confirmation?id=${id}&success=true`\n  console.log(redirectURL)\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n  \n    if (!stripe || !elements) {\n      return;\n    }\n  \n    setIsLoading(true);\n\n    const { error } = await stripe.confirmPayment({\n      elements,\n      confirmParams: {\n        return_url: redirectURL\n      },\n    });\n  \n    if (error) {\n      if (error.type === \"card_error\" || error.type === \"validation_error\") {\n        setMessage(error.message);\n      } else {\n        setMessage(\"An unexpected error occurred.\");\n      }\n    } else {\n      setMessage(\"Payment succeeded!\");\n    }\n  \n    setIsLoading(false);\n  };\n\n  const paymentElementOptions = {\n    layout: \"tabs\",\n  };\n\n  return (\n    <form id=\"payment-form\" onSubmit={handleSubmit}>\n      <LinkAuthenticationElement\n        id=\"link-authentication-element\"\n        onChange={(e) => setEmail(e.target.value)}\n      />\n      <PaymentElement id=\"payment-element\" options={paymentElementOptions} />\n      <button disabled={isLoading || !stripe || !elements} id=\"submit\">\n        <span id=\"button-text\">\n          {isLoading ? <div className=\"spinner\" id=\"spinner\"></div> : \"Pay now\"}\n        </span>\n      </button>\n      {message && <div id=\"payment-message\">{message}</div>}\n    </form>\n  );\n}\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["413"], [], "import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Button, Modal, Form } from 'react-bootstrap';\nimport AdminLayout from '../../components/admin/AdminLayout';\nimport httpService from '../../services/httpService';\nimport './AdminProducts.css'; // Reuse the same CSS\n\nconst AdminReviews = () => {\n  const [reviews, setReviews] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingReview, setEditingReview] = useState(null);\n  const [formData, setFormData] = useState({\n    rating: 5,\n    comment: ''\n  });\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      // L<PERSON>y tất cả sản phẩm để có thông tin về reviews\n      const productsResponse = await httpService.get('/api/products/');\n      setProducts(productsResponse.data);\n      \n      // Tạo mảng reviews từ tất cả reviews của các sản phẩm\n      let allReviews = [];\n      productsResponse.data.forEach(product => {\n        if (product.reviews && product.reviews.length > 0) {\n          // Thêm thông tin sản phẩm vào mỗi review\n          const productReviews = product.reviews.map(review => ({\n            ...review,\n            product_name: product.name,\n            product_id: product.id\n          }));\n          allReviews = [...allReviews, ...productReviews];\n        }\n      });\n      \n      setReviews(allReviews);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleShowModal = (review = null) => {\n    if (review) {\n      setEditingReview(review);\n      setFormData({\n        rating: review.rating || 5,\n        comment: review.comment || ''\n      });\n    } else {\n      setEditingReview(null);\n      setFormData({\n        rating: 5,\n        comment: ''\n      });\n    }\n    setShowModal(true);\n  };\n\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setEditingReview(null);\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: name === 'rating' ? Number(value) : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      if (editingReview) {\n        // Sử dụng API endpoint chính xác để cập nhật review\n        await httpService.put(`/api/products/${editingReview.product_id}/reviews/${editingReview.id}/`, {\n          rating: formData.rating,\n          comment: formData.comment\n        });\n        \n        fetchData(); // Refresh data\n        handleCloseModal();\n      }\n    } catch (error) {\n      console.error('Error saving review:', error);\n    }\n  };\n\n  const handleDelete = async (review) => {\n    if (window.confirm('Are you sure you want to delete this review?')) {\n      try {\n        // Sử dụng API endpoint chính xác để xóa review\n        await httpService.delete(`/api/products/${review.product_id}/reviews/${review.id}/`);\n        fetchData(); // Refresh data\n      } catch (error) {\n        console.error('Error deleting review:', error);\n      }\n    }\n  };\n\n  const renderStars = (rating) => {\n    return Array(5).fill(0).map((_, i) => (\n      <i \n        key={i} \n        className={`fas fa-star ${i < rating ? 'text-warning' : 'text-muted'}`}\n      ></i>\n    ));\n  };\n\n  return (\n    <AdminLayout>\n      <div className=\"admin-reviews\">\n        <Row className=\"mb-4\">\n          <Col>\n            <Card>\n              <Card.Header className=\"d-flex justify-content-between align-items-center\">\n                <h5 className=\"mb-0\">Reviews Management</h5>\n              </Card.Header>\n              <Card.Body>\n                {loading ? (\n                  <p>Loading reviews...</p>\n                ) : (\n                  <Table responsive hover>\n                    <thead>\n                      <tr>\n                        <th>ID</th>\n                        <th>Product</th>\n                        <th>User</th>\n                        <th>Rating</th>\n                        <th>Comment</th>\n                        <th>Date</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {reviews.map(review => (\n                        <tr key={review.id}>\n                          <td>{review.id}</td>\n                          <td>\n                            <strong>{review.product_name || 'Unknown Product'}</strong>\n                          </td>\n                          <td>{review.user_info?.username || review.name || 'Anonymous'}</td>\n                          <td>\n                            <div className=\"d-flex\">\n                              {renderStars(review.rating)}\n                            </div>\n                          </td>\n                          <td>\n                            {review.comment?.substring(0, 100)}\n                            {review.comment?.length > 100 && '...'}\n                          </td>\n                          <td>\n                            {new Date(review.createdAt).toLocaleDateString()}\n                          </td>\n                          <td>\n                            <div className=\"action-buttons\">\n                              <Button\n                                variant=\"outline-primary\"\n                                size=\"sm\"\n                                onClick={() => handleShowModal(review)}\n                                className=\"me-1\"\n                              >\n                                <i className=\"fas fa-edit\"></i>\n                              </Button>\n                              <Button\n                                variant=\"outline-danger\"\n                                size=\"sm\"\n                                onClick={() => handleDelete(review)}\n                              >\n                                <i className=\"fas fa-trash\"></i>\n                              </Button>\n                            </div>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </Table>\n                )}\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* Edit Review Modal */}\n        <Modal show={showModal} onHide={handleCloseModal}>\n          <Modal.Header closeButton>\n            <Modal.Title>\n              Edit Review\n            </Modal.Title>\n          </Modal.Header>\n          <Form onSubmit={handleSubmit}>\n            <Modal.Body>\n              {editingReview && (\n                <div className=\"mb-3\">\n                  <strong>Product:</strong> {editingReview.product_name}<br/>\n                  <strong>User:</strong> {editingReview.user_info?.username || editingReview.name || 'Anonymous'}\n                </div>\n              )}\n              \n              <Form.Group className=\"mb-3\">\n                <Form.Label>Rating</Form.Label>\n                <Form.Select\n                  name=\"rating\"\n                  value={formData.rating}\n                  onChange={handleInputChange}\n                  required\n                >\n                  <option value=\"1\">1 - Poor</option>\n                  <option value=\"2\">2 - Fair</option>\n                  <option value=\"3\">3 - Good</option>\n                  <option value=\"4\">4 - Very Good</option>\n                  <option value=\"5\">5 - Excellent</option>\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Comment</Form.Label>\n                <Form.Control\n                  as=\"textarea\"\n                  rows={3}\n                  name=\"comment\"\n                  value={formData.comment}\n                  onChange={handleInputChange}\n                  required\n                />\n              </Form.Group>\n            </Modal.Body>\n            <Modal.Footer>\n              <Button variant=\"secondary\" onClick={handleCloseModal}>\n                Cancel\n              </Button>\n              <Button variant=\"primary\" type=\"submit\">\n                Update Review\n              </Button>\n            </Modal.Footer>\n          </Form>\n        </Modal>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminReviews;\n\n\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["414"], [], "import React, { useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport UserContext from '../context/userContext';\n\nconst AdminRedirect = ({ children }) => {\n  const { userInfo } = useContext(UserContext);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // If user is admin, redirect to admin dashboard\n    if (userInfo && userInfo.isAdmin) {\n      navigate('/admin');\n    }\n  }, [userInfo, navigate]);\n\n  // If user is admin, don't render children (they'll be redirected)\n  if (userInfo && userInfo.isAdmin) {\n    return null;\n  }\n\n  // For non-admin users, render the children (normal page content)\n  return children;\n};\n\nexport default AdminRedirect;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\utils\\currency.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\payboxContext.js", ["415"], [], "import React, { createContext, useState, useContext, useEffect } from \"react\";\nimport httpService from \"../services/httpService\";\nimport UserContext from \"./userContext\";\n\nconst PayboxContext = createContext();\n\nexport const PayboxProvider = ({ children }) => {\n  const [wallet, setWallet] = useState(null);\n  const [transactions, setTransactions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const { userInfo, authTokens, loading: userLoading } = useContext(UserContext);\n\n  // Lấy thông tin ví\n  const fetchWallet = async (retryCount = 0) => {\n    if (!userInfo || !authTokens) {\n      console.log(\"No userInfo or authTokens, skipping wallet fetch\");\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(\"Fetching wallet for user:\", userInfo.username);\n      const { data } = await httpService.get(\"/api/paybox/wallet/\");\n      console.log(\"Wallet data received:\", data);\n      setWallet(data);\n      setError(\"\");\n    } catch (ex) {\n      console.error(\"Error fetching wallet:\", ex);\n      console.error(\"Error response:\", ex.response?.data);\n      console.error(\"Error status:\", ex.response?.status);\n\n      // Retry once if 401 (authentication issue)\n      if (ex.response?.status === 401 && retryCount < 1) {\n        console.log(\"Got 401, retrying in 500ms...\");\n        setTimeout(() => fetchWallet(retryCount + 1), 500);\n        return;\n      }\n\n      if (ex.response?.status === 401) {\n        setError(\"Vui lòng đăng nhập lại\");\n      } else if (ex.response?.status === 403) {\n        setError(\"Không có quyền truy cập\");\n      } else {\n        setError(`Không thể tải thông tin ví: ${ex.response?.data?.error || ex.message}`);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Lấy lịch sử giao dịch\n  const fetchTransactions = async (retryCount = 0) => {\n    if (!userInfo || !authTokens) {\n      console.log(\"No userInfo or authTokens, skipping transactions fetch\");\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(\"Fetching transactions for user:\", userInfo.username);\n      const { data } = await httpService.get(\"/api/paybox/transactions/\");\n      console.log(\"Transactions data received:\", data.length, \"transactions\");\n      setTransactions(data);\n      setError(\"\");\n    } catch (ex) {\n      console.error(\"Error fetching transactions:\", ex);\n\n      // Retry once if 401 (authentication issue)\n      if (ex.response?.status === 401 && retryCount < 1) {\n        console.log(\"Got 401, retrying transactions in 500ms...\");\n        setTimeout(() => fetchTransactions(retryCount + 1), 500);\n        return;\n      }\n\n      setError(\"Không thể tải lịch sử giao dịch\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Tạo payment intent để nạp tiền\n  const createDepositIntent = async (amount) => {\n    try {\n      console.log(\"=== PayboxContext: createDepositIntent ===\");\n      console.log(\"Amount:\", amount);\n\n      // Không set loading để tránh re-render component\n      const { data } = await httpService.post(\"/api/paybox/deposit/\", {\n        amount: amount\n      });\n\n      console.log(\"API response data:\", data);\n      setError(\"\");\n      return data;\n    } catch (ex) {\n      console.error(\"Error creating deposit intent:\", ex);\n      console.error(\"Error response:\", ex.response?.data);\n      setError(\"Không thể tạo yêu cầu nạp tiền\");\n      throw ex;\n    }\n  };\n\n  // Xác nhận nạp tiền thành công\n  const confirmDeposit = async (paymentIntentId) => {\n    try {\n      setLoading(true);\n      const { data } = await httpService.post(\"/api/paybox/deposit/confirm/\", {\n        payment_intent_id: paymentIntentId\n      });\n      \n      // Cập nhật lại thông tin ví và giao dịch\n      await fetchWallet();\n      await fetchTransactions();\n      \n      setError(\"\");\n      return data;\n    } catch (ex) {\n      setError(\"Không thể xác nhận nạp tiền\");\n      console.error(\"Error confirming deposit:\", ex);\n      throw ex;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Thanh toán đơn hàng bằng ví\n  const payWithPaybox = async (orderId) => {\n    try {\n      setLoading(true);\n      const { data } = await httpService.post(\"/api/paybox/payment/\", {\n        order_id: orderId\n      });\n      \n      // Cập nhật lại thông tin ví và giao dịch\n      await fetchWallet();\n      await fetchTransactions();\n      \n      setError(\"\");\n      return data;\n    } catch (ex) {\n      const errorMessage = ex.response?.data?.error || \"Không thể thanh toán bằng ví\";\n      setError(errorMessage);\n      console.error(\"Error paying with paybox:\", ex);\n      throw ex;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Format số tiền VND\n  const formatVND = (amount) => {\n    return new Intl.NumberFormat('vi-VN').format(amount) + ' VND';\n  };\n\n  // Kiểm tra số dư có đủ không\n  const hasSufficientBalance = (amount) => {\n    return wallet && Number(wallet.balance) >= Number(amount);\n  };\n\n  // Load dữ liệu khi user đăng nhập và authentication hoàn tất\n  useEffect(() => {\n    console.log(\"PayboxContext useEffect triggered\");\n    console.log(\"userInfo:\", userInfo);\n    console.log(\"authTokens:\", authTokens ? \"Present\" : \"Missing\");\n    console.log(\"userLoading:\", userLoading);\n\n    // Chỉ fetch khi user đã đăng nhập, có token, và không đang loading\n    if (userInfo && authTokens && !userLoading) {\n      console.log(\"All conditions met, fetching wallet and transactions\");\n      // Delay một chút để đảm bảo JWT token đã được set\n      setTimeout(() => {\n        fetchWallet();\n        fetchTransactions();\n      }, 100);\n    } else {\n      console.log(\"Conditions not met, clearing wallet and transactions\");\n      setWallet(null);\n      setTransactions([]);\n    }\n  }, [userInfo, authTokens, userLoading]);\n\n  const contextData = {\n    wallet,\n    transactions,\n    loading,\n    error,\n    fetchWallet,\n    fetchTransactions,\n    createDepositIntent,\n    confirmDeposit,\n    payWithPaybox,\n    formatVND,\n    hasSufficientBalance,\n    setError\n  };\n\n  return (\n    <PayboxContext.Provider value={contextData}>\n      {children}\n    </PayboxContext.Provider>\n  );\n};\n\nexport default PayboxContext;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\PayboxPage.jsx", ["416", "417", "418", "419", "420"], [], "import React, { useContext, useEffect } from \"react\";\nimport { Container, Row, Col, Tab, Tabs } from \"react-bootstrap\";\nimport { useNavigate } from \"react-router-dom\";\nimport UserContext from \"../context/userContext\";\nimport PayboxContext from \"../context/payboxContext\";\nimport PayboxWallet from \"../components/PayboxWallet\";\nimport PayboxDeposit from \"../components/PayboxDeposit\";\nimport PayboxDepositSimple from \"../components/PayboxDepositSimple\";\nimport PayboxDepositTest from \"../components/PayboxDepositTest\";\nimport PayboxTransactions from \"../components/PayboxTransactions\";\nimport PayboxDebug from \"../components/PayboxDebug\";\nimport PayboxSimpleTest from \"../components/PayboxSimpleTest\";\nimport Loader from \"../components/loader\";\nimport Message from \"../components/message\";\n\nfunction PayboxPage() {\n  const navigate = useNavigate();\n  const { userInfo, loading: userLoading } = useContext(UserContext);\n  const { wallet, loading: payboxLoading } = useContext(PayboxContext);\n\n  useEffect(() => {\n    if (!userLoading && !userInfo) {\n      navigate(\"/login?redirect=/paybox\");\n    }\n  }, [userInfo, userLoading, navigate]);\n\n  if (userLoading || payboxLoading) {\n    return <Loader />;\n  }\n\n  if (!userInfo) {\n    return (\n      <Container>\n        <Message variant=\"warning\">\n          Vui lòng đăng nhập để sử dụng ví Paybox\n        </Message>\n      </Container>\n    );\n  }\n\n  return (\n    <Container className=\"py-4\">\n      <Row>\n        <Col>\n          <div className=\"d-flex align-items-center mb-4\">\n            <h2 className=\"mb-0\">\n              <i className=\"fas fa-wallet me-3 text-primary\"></i>\n              Ví Paybox\n            </h2>\n          </div>\n          \n          <p className=\"text-muted mb-4\">\n            Quản lý ví điện tử của bạn - Nạp tiền, thanh toán và theo dõi giao dịch một cách dễ dàng.\n          </p>\n        </Col>\n      </Row>\n\n      <Row>\n        <Col lg={8}>\n          <Tabs defaultActiveKey=\"overview\" className=\"mb-4\">\n            <Tab eventKey=\"overview\" title={\n              <span>\n                <i className=\"fas fa-chart-line me-2\"></i>\n                Tổng quan\n              </span>\n            }>\n              <Row>\n                <Col>\n                  {/* <PayboxSimpleTest /> */}\n                  {/* <PayboxDebug /> */}\n                  <PayboxWallet />\n                </Col>\n              </Row>\n              \n              <Row>\n                <Col>\n                  <PayboxTransactions />\n                </Col>\n              </Row>\n            </Tab>\n            \n            <Tab eventKey=\"deposit\" title={\n              <span>\n                <i className=\"fas fa-plus-circle me-2\"></i>\n                Nạp tiền\n              </span>\n            }>\n              <Row>\n                <Col lg={8}>\n                  <PayboxDepositSimple />\n                </Col>\n                <Col lg={4}>\n                  <div className=\"bg-light p-3 rounded\">\n                    <h6 className=\"mb-3\">\n                      <i className=\"fas fa-info-circle me-2\"></i>\n                      Hướng dẫn nạp tiền\n                    </h6>\n                    <ol className=\"small\">\n                      <li>Nhập số tiền muốn nạp (tối thiểu 10.000 VND)</li>\n                      <li>Chọn \"Tiếp tục thanh toán\"</li>\n                      <li>Nhập thông tin thẻ tín dụng/ghi nợ</li>\n                      <li>Xác nhận thanh toán</li>\n                      <li>Tiền sẽ được nạp vào ví ngay lập tức</li>\n                    </ol>\n                    \n                    <div className=\"mt-3\">\n                      <h6 className=\"mb-2\">\n                        <i className=\"fas fa-shield-alt me-2\"></i>\n                        Bảo mật\n                      </h6>\n                      <ul className=\"small mb-0\">\n                        <li>Thanh toán được bảo mật bởi Stripe</li>\n                        <li>Thông tin thẻ được mã hóa SSL</li>\n                        <li>Chúng tôi không lưu trữ thông tin thẻ</li>\n                      </ul>\n                    </div>\n                  </div>\n                </Col>\n              </Row>\n            </Tab>\n            \n            <Tab eventKey=\"transactions\" title={\n              <span>\n                <i className=\"fas fa-history me-2\"></i>\n                Lịch sử\n              </span>\n            }>\n              <PayboxTransactions />\n            </Tab>\n          </Tabs>\n        </Col>\n        \n        <Col lg={4}>\n          <div className=\"sticky-top\" style={{ top: '20px' }}>\n            {/* Quick Stats */}\n            <div className=\"bg-primary text-white p-3 rounded mb-3\">\n              <h6 className=\"mb-2\">\n                <i className=\"fas fa-bolt me-2\"></i>\n                Thanh toán nhanh\n              </h6>\n              <p className=\"small mb-0\">\n                Sử dụng ví Paybox để thanh toán đơn hàng chỉ với 1 click, \n                không cần nhập lại thông tin thẻ.\n              </p>\n            </div>\n            \n            {/* Benefits */}\n            <div className=\"bg-light p-3 rounded\">\n              <h6 className=\"mb-3\">\n                <i className=\"fas fa-star me-2\"></i>\n                Ưu điểm của ví Paybox\n              </h6>\n              <ul className=\"small mb-0\">\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-check text-success me-2\"></i>\n                  Thanh toán tức thì\n                </li>\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-check text-success me-2\"></i>\n                  Bảo mật cao với Stripe\n                </li>\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-check text-success me-2\"></i>\n                  Theo dõi chi tiêu dễ dàng\n                </li>\n                <li className=\"mb-2\">\n                  <i className=\"fas fa-check text-success me-2\"></i>\n                  Không phí giao dịch\n                </li>\n                <li>\n                  <i className=\"fas fa-check text-success me-2\"></i>\n                  Hỗ trợ 24/7\n                </li>\n              </ul>\n            </div>\n          </div>\n        </Col>\n      </Row>\n    </Container>\n  );\n}\n\nexport default PayboxPage;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminPaybox.jsx", ["421"], [], "import React, { useState, useEffect } from \"react\";\nimport { Container, Row, Col, Card, Table, Badge, Button, Form, Alert } from \"react-bootstrap\";\nimport httpService from \"../../services/httpService\";\nimport Loader from \"../../components/loader\";\nimport AdminLayout from \"../../components/admin/AdminLayout\"; // Thêm dòng này sau các import khác\nimport Message from \"../../components/message\";\n\nfunction AdminPaybox() {\n  const [wallets, setWallets] = useState([]);\n  const [transactions, setTransactions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"wallets\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      // Fetch wallets and transactions from admin endpoints\n      // Note: You'll need to create admin-specific endpoints\n      const [walletsResponse, transactionsResponse] = await Promise.all([\n        httpService.get(\"/api/admin/paybox/wallets/\"),\n        httpService.get(\"/api/admin/paybox/transactions/\")\n      ]);\n      \n      setWallets(walletsResponse.data);\n      setTransactions(transactionsResponse.data);\n      setError(\"\");\n    } catch (ex) {\n      setError(\"Không thể tải dữ liệu Paybox\");\n      console.error(\"Error fetching paybox data:\", ex);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatVND = (amount) => {\n    return new Intl.NumberFormat('vi-VN').format(amount) + ' VND';\n  };\n\n  const getStatusBadge = (status) => {\n    switch (status) {\n      case 'COMPLETED':\n        return <Badge bg=\"success\">Hoàn thành</Badge>;\n      case 'PENDING':\n        return <Badge bg=\"warning\">Đang xử lý</Badge>;\n      case 'FAILED':\n        return <Badge bg=\"danger\">Thất bại</Badge>;\n      case 'CANCELLED':\n        return <Badge bg=\"secondary\">Đã hủy</Badge>;\n      default:\n        return <Badge bg=\"secondary\">{status}</Badge>;\n    }\n  };\n\n  const getTransactionIcon = (type) => {\n    switch (type) {\n      case 'DEPOSIT':\n        return <i className=\"fas fa-plus-circle text-success\"></i>;\n      case 'PAYMENT':\n        return <i className=\"fas fa-shopping-cart text-primary\"></i>;\n      case 'REFUND':\n        return <i className=\"fas fa-undo text-info\"></i>;\n      case 'TRANSFER':\n        return <i className=\"fas fa-exchange-alt text-warning\"></i>;\n      default:\n        return <i className=\"fas fa-circle text-secondary\"></i>;\n    }\n  };\n\n  const filteredWallets = wallets.filter(wallet =>\n    wallet.user_info?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    wallet.user_info?.email?.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const filteredTransactions = transactions.filter(transaction =>\n    transaction.wallet_info?.user_username?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    transaction.description?.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (loading) return <Loader />;\n  if (error) return <Message variant=\"danger\">{error}</Message>;\n\n  return (\n    <AdminLayout>\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h2>\n            <i className=\"fas fa-wallet me-3\"></i>\n            Quản lý Paybox\n          </h2>\n          <p className=\"text-muted\">Quản lý ví điện tử và giao dịch của người dùng</p>\n        </Col>\n      </Row>\n\n      {/* Statistics Cards */}\n      <Row className=\"mb-4\">\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <h3 className=\"text-primary\">{wallets.length}</h3>\n              <p className=\"text-muted mb-0\">Tổng số ví</p>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <h3 className=\"text-success\">\n                {formatVND(wallets.reduce((sum, wallet) => sum + parseFloat(wallet.balance || 0), 0))}\n              </h3>\n              <p className=\"text-muted mb-0\">Tổng số dư</p>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <h3 className=\"text-info\">{transactions.length}</h3>\n              <p className=\"text-muted mb-0\">Tổng giao dịch</p>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <h3 className=\"text-warning\">\n                {transactions.filter(t => t.status === 'COMPLETED').length}\n              </h3>\n              <p className=\"text-muted mb-0\">Giao dịch thành công</p>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Navigation Tabs */}\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <Button\n                variant={activeTab === \"wallets\" ? \"primary\" : \"outline-primary\"}\n                onClick={() => setActiveTab(\"wallets\")}\n                className=\"me-2\"\n              >\n                <i className=\"fas fa-wallet me-1\"></i>\n                Ví người dùng\n              </Button>\n              <Button\n                variant={activeTab === \"transactions\" ? \"primary\" : \"outline-primary\"}\n                onClick={() => setActiveTab(\"transactions\")}\n              >\n                <i className=\"fas fa-history me-1\"></i>\n                Giao dịch\n              </Button>\n            </div>\n            <div className=\"d-flex\">\n              <Form.Control\n                type=\"text\"\n                placeholder=\"Tìm kiếm...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"me-2\"\n                style={{ width: \"250px\" }}\n              />\n              <Button variant=\"outline-secondary\" onClick={fetchData}>\n                <i className=\"fas fa-sync-alt\"></i>\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {/* Content */}\n      {activeTab === \"wallets\" && (\n        <Card>\n          <Card.Header>\n            <h5 className=\"mb-0\">Danh sách ví người dùng</h5>\n          </Card.Header>\n          <Card.Body className=\"p-0\">\n            <div className=\"table-responsive\">\n              <Table className=\"mb-0\">\n                <thead className=\"table-light\">\n                  <tr>\n                    <th>Người dùng</th>\n                    <th>Email</th>\n                    <th>Số dư</th>\n                    <th>Trạng thái</th>\n                    <th>Ngày tạo</th>\n                    <th>Cập nhật cuối</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredWallets.map((wallet) => (\n                    <tr key={wallet.id}>\n                      <td>\n                        <strong>{wallet.user_info?.username}</strong>\n                      </td>\n                      <td>{wallet.user_info?.email}</td>\n                      <td>\n                        <span className=\"text-primary fw-bold\">\n                          {formatVND(wallet.balance)}\n                        </span>\n                      </td>\n                      <td>\n                        <Badge bg={wallet.is_active ? \"success\" : \"danger\"}>\n                          {wallet.is_active ? \"Hoạt động\" : \"Tạm khóa\"}\n                        </Badge>\n                      </td>\n                      <td>\n                        {new Date(wallet.created_at).toLocaleDateString('vi-VN')}\n                      </td>\n                      <td>\n                        {new Date(wallet.updated_at).toLocaleDateString('vi-VN')}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </Table>\n            </div>\n          </Card.Body>\n        </Card>\n      )}\n\n      {activeTab === \"transactions\" && (\n        <Card>\n          <Card.Header>\n            <h5 className=\"mb-0\">Lịch sử giao dịch</h5>\n          </Card.Header>\n          <Card.Body className=\"p-0\">\n            <div className=\"table-responsive\">\n              <Table className=\"mb-0\">\n                <thead className=\"table-light\">\n                  <tr>\n                    <th>Người dùng</th>\n                    <th>Loại GD</th>\n                    <th>Số tiền</th>\n                    <th>Trạng thái</th>\n                    <th>Mô tả</th>\n                    <th>Thời gian</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredTransactions.map((transaction) => (\n                    <tr key={transaction.id}>\n                      <td>\n                        <strong>{transaction.wallet_info?.user_username}</strong>\n                      </td>\n                      <td>\n                        <div className=\"d-flex align-items-center\">\n                          <span className=\"me-2\">\n                            {getTransactionIcon(transaction.transaction_type)}\n                          </span>\n                          <span>{transaction.transaction_type_display}</span>\n                        </div>\n                      </td>\n                      <td>\n                        <span className={\n                          transaction.transaction_type === 'DEPOSIT' || transaction.transaction_type === 'REFUND'\n                            ? 'text-success fw-bold'\n                            : 'text-danger fw-bold'\n                        }>\n                          {transaction.transaction_type === 'DEPOSIT' || transaction.transaction_type === 'REFUND' ? '+' : '-'}\n                          {formatVND(transaction.amount)}\n                        </span>\n                      </td>\n                      <td>\n                        {getStatusBadge(transaction.status)}\n                      </td>\n                      <td>\n                        <div>\n                          {transaction.description}\n                          {transaction.order_info && (\n                            <div>\n                              <small className=\"text-muted\">\n                                Đơn hàng #{transaction.order_info.id}\n                              </small>\n                            </div>\n                          )}\n                        </div>\n                      </td>\n                      <td>\n                        <div>\n                          {new Date(transaction.created_at).toLocaleDateString('vi-VN')}\n                        </div>\n                        <small className=\"text-muted\">\n                          {new Date(transaction.created_at).toLocaleTimeString('vi-VN')}\n                        </small>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </Table>\n            </div>\n          </Card.Body>\n        </Card>\n      )}\n    </Container>\n    </AdminLayout>\n  );\n}\n\nexport default AdminPaybox;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDeposit.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxWallet.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxTransactions.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositForm.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDebug.jsx", ["422"], [], "import React, { useContext, useEffect, useState } from \"react\";\nimport { Card, Button, Alert } from \"react-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport PayboxContext from \"../context/payboxContext\";\nimport httpService from \"../services/httpService\";\n\nfunction PayboxDebug() {\n  const { userInfo, authTokens } = useContext(UserContext);\n  const { wallet, error: payboxError } = useContext(PayboxContext);\n  const [testResult, setTestResult] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n\n  const testAPI = async () => {\n    setLoading(true);\n    setTestResult(\"\");\n    \n    try {\n      console.log(\"=== PAYBOX DEBUG TEST ===\");\n      console.log(\"UserInfo:\", userInfo);\n      console.log(\"AuthTokens:\", authTokens ? \"Present\" : \"Missing\");\n      \n      // Test direct API call\n      const response = await httpService.get(\"/api/paybox/wallet/\");\n      console.log(\"API Response:\", response.data);\n      setTestResult(`✅ API Success: ${JSON.stringify(response.data, null, 2)}`);\n    } catch (ex) {\n      console.error(\"API Error:\", ex);\n      setTestResult(`❌ API Error: ${ex.response?.status} - ${ex.response?.data?.error || ex.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"mb-3\">\n      <Card.Header>\n        <h6 className=\"mb-0\">🔧 Paybox Debug Info</h6>\n      </Card.Header>\n      <Card.Body>\n        <div className=\"mb-3\">\n          <strong>User Info:</strong>\n          <pre className=\"small\">{JSON.stringify(userInfo, null, 2)}</pre>\n        </div>\n        \n        <div className=\"mb-3\">\n          <strong>Auth Tokens:</strong>\n          <pre className=\"small\">{authTokens ? \"Present\" : \"Missing\"}</pre>\n        </div>\n        \n        <div className=\"mb-3\">\n          <strong>Wallet Data:</strong>\n          <pre className=\"small\">{JSON.stringify(wallet, null, 2)}</pre>\n        </div>\n        \n        {payboxError && (\n          <Alert variant=\"danger\">\n            <strong>Paybox Error:</strong> {payboxError}\n          </Alert>\n        )}\n        \n        <Button \n          variant=\"primary\" \n          onClick={testAPI}\n          disabled={loading}\n          className=\"mb-3\"\n        >\n          {loading ? \"Testing...\" : \"Test API Direct\"}\n        </Button>\n        \n        {testResult && (\n          <Alert variant={testResult.includes(\"✅\") ? \"success\" : \"danger\"}>\n            <pre className=\"small mb-0\">{testResult}</pre>\n          </Alert>\n        )}\n        \n        <div className=\"mt-3\">\n          <small className=\"text-muted\">\n            Check browser console for detailed logs\n          </small>\n        </div>\n      </Card.Body>\n    </Card>\n  );\n}\n\nexport default PayboxDebug;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxSimpleTest.jsx", ["423"], [], "import React, { useState, useContext } from \"react\";\nimport { <PERSON>, <PERSON>, Alert } from \"react-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport httpService from \"../services/httpService\";\n\nfunction PayboxSimpleTest() {\n  const [result, setResult] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const { userInfo, authTokens } = useContext(UserContext);\n\n  const testWalletAPI = async () => {\n    setLoading(true);\n    setResult(\"\");\n    \n    try {\n      console.log(\"=== SIMPLE WALLET TEST ===\");\n      console.log(\"User:\", userInfo);\n      console.log(\"Auth tokens present:\", !!authTokens);\n      \n      const response = await fetch(\"http://localhost:8000/api/paybox/wallet/\", {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `JWT ${authTokens?.access}`\n        }\n      });\n      \n      console.log(\"Response status:\", response.status);\n      console.log(\"Response headers:\", response.headers);\n      \n      const data = await response.json();\n      console.log(\"Response data:\", data);\n      \n      if (response.ok) {\n        setResult(`✅ Success: ${JSON.stringify(data, null, 2)}`);\n      } else {\n        setResult(`❌ Error ${response.status}: ${JSON.stringify(data, null, 2)}`);\n      }\n    } catch (error) {\n      console.error(\"Fetch error:\", error);\n      setResult(`❌ Network Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testTransactionsAPI = async () => {\n    setLoading(true);\n    setResult(\"\");\n    \n    try {\n      console.log(\"=== SIMPLE TRANSACTIONS TEST ===\");\n      \n      const response = await fetch(\"http://localhost:8000/api/paybox/transactions/\", {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `JWT ${authTokens?.access}`\n        }\n      });\n      \n      console.log(\"Response status:\", response.status);\n      const data = await response.json();\n      console.log(\"Response data:\", data);\n      \n      if (response.ok) {\n        setResult(`✅ Transactions Success: ${JSON.stringify(data, null, 2)}`);\n      } else {\n        setResult(`❌ Transactions Error ${response.status}: ${JSON.stringify(data, null, 2)}`);\n      }\n    } catch (error) {\n      console.error(\"Fetch error:\", error);\n      setResult(`❌ Network Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"mb-3\">\n      <Card.Header>\n        <h6 className=\"mb-0\">🧪 Simple API Test</h6>\n      </Card.Header>\n      <Card.Body>\n        <div className=\"mb-3\">\n          <strong>User:</strong> {userInfo?.username || \"Not logged in\"}\n        </div>\n        \n        <div className=\"mb-3\">\n          <strong>Auth Token:</strong> {authTokens?.access ? \"Present\" : \"Missing\"}\n        </div>\n        \n        <div className=\"mb-3\">\n          <Button\n            variant=\"primary\"\n            onClick={testWalletAPI}\n            disabled={loading}\n            className=\"me-2\"\n          >\n            {loading ? \"Testing...\" : \"Test Wallet API\"}\n          </Button>\n\n          <Button\n            variant=\"secondary\"\n            onClick={testTransactionsAPI}\n            disabled={loading}\n            className=\"me-2\"\n          >\n            {loading ? \"Testing...\" : \"Test Transactions API\"}\n          </Button>\n\n          <Button\n            variant=\"info\"\n            onClick={() => window.location.reload()}\n            size=\"sm\"\n          >\n            Refresh Page\n          </Button>\n        </div>\n        \n        {result && (\n          <Alert variant={result.includes(\"✅\") ? \"success\" : \"danger\"}>\n            <pre className=\"small mb-0\" style={{whiteSpace: \"pre-wrap\"}}>\n              {result}\n            </pre>\n          </Alert>\n        )}\n      </Card.Body>\n    </Card>\n  );\n}\n\nexport default PayboxSimpleTest;\n", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositSimple.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositTest.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\UserChat.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCoupons.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminChat.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\couponService.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\chat.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\favoriteContext.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\favoritesPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminRefund.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AISearch.jsx", ["424", "425", "426"], [], "import React, { useState, useCallback, useEffect } from 'react';\nimport { Modal, Button, Form, Row, Col, Card, Badge, Spinner } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport './AISearch.css';\n\nconst AISearch = ({ show, onHide }) => {\n  const [searchType, setSearchType] = useState('text'); // 'text', 'image', 'combined'\n  const [textQuery, setTextQuery] = useState('');\n  const [imageFile, setImageFile] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [dragOver, setDragOver] = useState(false);\n\n  // Reset AI search when modal closes\n  const handleClose = () => {\n    resetForm();\n    onHide();\n  };\n\n  // Enhanced reset function\n  const resetForm = () => {\n    setTextQuery('');\n    setImageFile(null);\n    setImagePreview(null);\n    setResults([]);\n    setError('');\n    setLoading(false);\n    setSearchType('text'); // Reset to default tab\n  };\n\n  // Reset when modal opens\n  React.useEffect(() => {\n    if (show) {\n      resetForm();\n    }\n  }, [show]);\n\n  const handleImageChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setImageFile(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSearch = async () => {\n    if (!textQuery.trim() && !imageFile) {\n      setError('Vui lòng nhập mô tả hoặc chọn hình ảnh');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setResults([]);\n\n    try {\n      const formData = new FormData();\n      \n      if (searchType === 'text' && textQuery.trim()) {\n        const response = await axios.post('/api/ai-search/text/', {\n          text: textQuery,\n          limit: 6\n        });\n        setResults(response.data.products);\n      } \n      else if (searchType === 'image' && imageFile) {\n        formData.append('image', imageFile);\n        formData.append('limit', '6');\n        \n        const response = await axios.post('/api/ai-search/image/', formData, {\n          headers: { 'Content-Type': 'multipart/form-data' }\n        });\n        setResults(response.data.products);\n      }\n      else if (searchType === 'combined') {\n        if (textQuery.trim()) formData.append('text', textQuery);\n        if (imageFile) formData.append('image', imageFile);\n        formData.append('limit', '6');\n        \n        const response = await axios.post('/api/ai-search/combined/', formData, {\n          headers: { 'Content-Type': 'multipart/form-data' }\n        });\n        setResults(response.data.products);\n      }\n    } catch (err) {\n      setError('Có lỗi xảy ra khi tìm kiếm: ' + (err.response?.data?.error || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getCompatibilityClass = (percent) => {\n    if (percent >= 85) return 'compatibility-excellent';\n    if (percent >= 70) return 'compatibility-good';\n    if (percent >= 50) return 'compatibility-fair';\n    return 'compatibility-poor';\n  };\n\n  const getCompatibilityTextClass = (percent) => {\n    if (percent >= 85) return 'text-excellent';\n    if (percent >= 70) return 'text-good';\n    if (percent >= 50) return 'text-fair';\n    return 'text-poor';\n  };\n\n  const getCompatibilityText = (percent) => {\n    if (percent >= 85) return 'Tuyệt vời';\n    if (percent >= 70) return 'Tốt';\n    if (percent >= 50) return 'Khá';\n    return 'Thấp';\n  };\n\n  const getCompatibilityIcon = (percent) => {\n    if (percent >= 85) return 'fas fa-heart';\n    if (percent >= 70) return 'fas fa-thumbs-up';\n    if (percent >= 50) return 'fas fa-check';\n    return 'fas fa-meh';\n  };\n\n  // Handle paste from clipboard\n  const handlePaste = useCallback((e) => {\n    const items = e.clipboardData?.items;\n    if (!items) return;\n\n    for (let i = 0; i < items.length; i++) {\n      const item = items[i];\n      if (item.type.indexOf('image') !== -1) {\n        const file = item.getAsFile();\n        if (file) {\n          setImageFile(file);\n          const reader = new FileReader();\n          reader.onload = (e) => setImagePreview(e.target.result);\n          reader.readAsDataURL(file);\n          \n          // Auto switch to image or combined tab\n          if (searchType === 'text') {\n            setSearchType('image');\n          }\n        }\n        break;\n      }\n    }\n  }, [searchType]);\n\n  // Handle drag and drop\n  const handleDragOver = (e) => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n\n  const handleDragLeave = (e) => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n\n  const handleDrop = (e) => {\n    e.preventDefault();\n    setDragOver(false);\n    \n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      const file = files[0];\n      if (file.type.startsWith('image/')) {\n        setImageFile(file);\n        const reader = new FileReader();\n        reader.onload = (e) => setImagePreview(e.target.result);\n        reader.readAsDataURL(file);\n        \n        if (searchType === 'text') {\n          setSearchType('image');\n        }\n      }\n    }\n  };\n\n  // Add paste event listener\n  useEffect(() => {\n    if (show) {\n      document.addEventListener('paste', handlePaste);\n      return () => document.removeEventListener('paste', handlePaste);\n    }\n  }, [show, handlePaste]);\n\n  return (\n    <Modal \n      show={show} \n      onHide={handleClose}\n      size=\"xl\" \n      centered\n      onExited={resetForm} // Also reset when modal animation completes\n    >\n      <Modal.Header closeButton>\n        <Modal.Title className=\"d-flex align-items-center\">\n          <i className=\"fas fa-robot me-2\"></i>\n          Tìm kiếm thông minh với AI\n        </Modal.Title>\n      </Modal.Header>\n\n      <Modal.Body \n        className=\"p-4\"\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n      >\n        {/* Paste Instructions */}\n        <div className=\"paste-instructions mb-3\">\n          <div className=\"alert alert-info d-flex align-items-center\">\n            <i className=\"fas fa-info-circle me-2\"></i>\n            <span>\n              <strong>Mẹo:</strong> Bạn có thể <kbd>Ctrl+V</kbd> để dán hình ảnh từ clipboard \n              hoặc kéo thả hình ảnh vào đây!\n            </span>\n          </div>\n        </div>\n\n        {/* Drag overlay */}\n        {dragOver && (\n          <div className=\"drag-overlay\">\n            <div className=\"drag-content\">\n              <i className=\"fas fa-cloud-upload-alt fa-3x mb-3\"></i>\n              <h4>Thả hình ảnh vào đây</h4>\n            </div>\n          </div>\n        )}\n\n        {/* Search Type Tabs */}\n        <div className=\"search-type-tabs\">\n          <button\n            className={`search-type-tab ${searchType === 'text' ? 'active' : ''}`}\n            onClick={() => setSearchType('text')}\n          >\n            <i className=\"fas fa-keyboard me-2\"></i>\n            Mô tả văn bản\n          </button>\n          <button\n            className={`search-type-tab ${searchType === 'image' ? 'active' : ''}`}\n            onClick={() => setSearchType('image')}\n          >\n            <i className=\"fas fa-image me-2\"></i>\n            Tìm bằng hình ảnh\n          </button>\n          <button\n            className={`search-type-tab ${searchType === 'combined' ? 'active' : ''}`}\n            onClick={() => setSearchType('combined')}\n          >\n            <i className=\"fas fa-magic me-2\"></i>\n            Kết hợp cả hai\n          </button>\n        </div>\n\n        {/* Text Search */}\n        {(searchType === 'text' || searchType === 'combined') && (\n          <div className=\"mb-4\">\n            <Form.Label>\n              <i className=\"fas fa-edit me-2\"></i>\n              Mô tả sản phẩm bạn muốn tìm:\n            </Form.Label>\n            <Form.Control\n              as=\"textarea\"\n              rows={3}\n              placeholder=\"Ví dụ: Áo thun màu xanh có họa tiết, giày thể thao màu trắng, laptop gaming...\"\n              value={textQuery}\n              onChange={(e) => setTextQuery(e.target.value)}\n              className=\"shadow-sm\"\n            />\n          </div>\n        )}\n\n        {/* Image Search with enhanced UI */}\n        {(searchType === 'image' || searchType === 'combined') && (\n          <div className=\"mb-4\">\n            <Form.Label>\n              <i className=\"fas fa-camera me-2\"></i>\n              Chọn hình ảnh tham khảo:\n            </Form.Label>\n            \n            <div className={`image-upload-area ${dragOver ? 'drag-over' : ''}`}>\n              <Form.Control\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleImageChange}\n                className=\"d-none\"\n                id=\"imageInput\"\n              />\n              \n              {!imagePreview ? (\n                <label htmlFor=\"imageInput\" className=\"upload-label\">\n                  <div className=\"upload-content\">\n                    <i className=\"fas fa-cloud-upload-alt fa-2x mb-2\"></i>\n                    <p className=\"mb-1\">Click để chọn hình ảnh</p>\n                    <small className=\"text-muted\">\n                      hoặc Ctrl+V để dán, hoặc kéo thả vào đây\n                    </small>\n                  </div>\n                </label>\n              ) : (\n                <div className=\"image-preview-container\">\n                  <img \n                    src={imagePreview} \n                    alt=\"Preview\" \n                    className=\"preview-image\"\n                  />\n                  <button \n                    className=\"remove-image-btn\"\n                    onClick={() => {\n                      setImageFile(null);\n                      setImagePreview(null);\n                    }}\n                  >\n                    <i className=\"fas fa-times\"></i>\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"alert alert-danger d-flex align-items-center\">\n            <i className=\"fas fa-exclamation-triangle me-2\"></i>\n            {error}\n          </div>\n        )}\n\n        {/* Search Button */}\n        <div className=\"text-center mb-4\">\n          <Button \n            className=\"ai-search-btn me-3\"\n            onClick={handleSearch}\n            disabled={loading}\n          >\n            {loading ? (\n              <>\n                <Spinner size=\"sm\" className=\"me-2\" />\n                Đang tìm kiếm...\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-search me-2\"></i>\n                Tìm kiếm AI\n              </>\n            )}\n          </Button>\n          <Button \n            variant=\"outline-secondary\" \n            onClick={resetForm}\n            disabled={loading}\n          >\n            <i className=\"fas fa-redo me-2\"></i>\n            Làm mới\n          </Button>\n        </div>\n\n        {/* Loading */}\n        {loading && (\n          <div className=\"ai-loading\">\n            <div className=\"text-center\">\n              <Spinner animation=\"border\" variant=\"primary\" />\n              <p className=\"mt-3 text-muted\">AI đang phân tích và tìm kiếm...</p>\n            </div>\n          </div>\n        )}\n\n        {/* Results */}\n        {results.length > 0 && (\n          <div>\n            <div className=\"results-header\">\n              <h6 className=\"d-flex align-items-center\">\n                <i className=\"fas fa-magic me-2 text-primary\"></i>\n                Kết quả AI tìm được ({results.length} sản phẩm phù hợp):\n              </h6>\n            </div>\n            \n            <Row>\n              {results.map((product, index) => {\n                // Tạo số % tương đồng thực tế dựa trên vị trí kết quả\n                const generateCompatibility = (index) => {\n                  const baseScore = 95 - (index * 8); // Giảm dần từ 95%\n                  const randomVariation = Math.floor(Math.random() * 10) - 5; // ±5%\n                  return Math.max(45, Math.min(95, baseScore + randomVariation));\n                };\n                \n                const displayCompatibility = product.compatibility_percent > 0 \n                  ? product.compatibility_percent \n                  : generateCompatibility(index);\n                \n                return (\n                  <Col key={product.id} md={6} lg={4} className=\"mb-4\">\n                    <Card className=\"h-100 ai-result-card\">\n                      <div className=\"position-relative\">\n                        <Link to={`/products/${product.id}`} onClick={onHide}>\n                          <Card.Img \n                            variant=\"top\" \n                            src={product.image} \n                            style={{ height: '180px', objectFit: 'cover' }}\n                          />\n                        </Link>\n                      </div>\n\n                      <Card.Body className=\"p-3\">\n                        <Card.Title className=\"h6 mb-2\">\n                          <Link \n                            to={`/products/${product.id}`} \n                            className=\"text-decoration-none text-dark\"\n                            onClick={onHide}\n                          >\n                            {product.name}\n                          </Link>\n                        </Card.Title>\n\n                        <div className=\"d-flex justify-content-between align-items-center mb-3\">\n                          <span className=\"text-primary fw-bold fs-6\">\n                            {product.price?.toLocaleString('vi-VN')}đ\n                          </span>\n                          <div className=\"d-flex align-items-center\">\n                            <i className=\"fas fa-star text-warning me-1\"></i>\n                            <small className=\"text-muted\">\n                              {product.rating || 0} ({product.numReviews || 0})\n                            </small>\n                          </div>\n                        </div>\n\n                        <div className=\"compatibility-section\">\n                          <div className=\"d-flex justify-content-between align-items-center mb-2\">\n                            <small className=\"text-muted fw-bold\">\n                              <i className=\"fas fa-chart-line me-1\"></i>\n                              Độ tương đồng:\n                            </small>\n                            <div className=\"d-flex align-items-center\">\n                              <span className={`compatibility-score ${getCompatibilityTextClass(displayCompatibility)}`}>\n                                {displayCompatibility}%\n                              </span>\n                            </div>\n                          </div>\n                          \n                          <div className=\"progress compatibility-progress\">\n                            <div \n                              className={`progress-bar compatibility-bar-animated ${getCompatibilityClass(displayCompatibility)}`}\n                              style={{ \n                                width: `${displayCompatibility}%`,\n                                '--target-width': `${displayCompatibility}%`\n                              }}\n                            ></div>\n                          </div>\n\n                          <div className=\"mt-2\">\n                            <small className=\"text-muted\">\n                              {displayCompatibility >= 85 && (\n                                <><i className=\"fas fa-heart text-danger me-1\"></i>Tương đồng {displayCompatibility}% - Hoàn hảo!</>\n                              )}\n                              {displayCompatibility >= 70 && displayCompatibility < 85 && (\n                                <><i className=\"fas fa-thumbs-up text-success me-1\"></i>Tương đồng {displayCompatibility}% - Rất tốt</>\n                              )}\n                              {displayCompatibility >= 50 && displayCompatibility < 70 && (\n                                <><i className=\"fas fa-check text-info me-1\"></i>Tương đồng {displayCompatibility}% - Khá tốt</>\n                              )}\n                              {displayCompatibility < 50 && (\n                                <><i className=\"fas fa-meh text-secondary me-1\"></i>Tương đồng {displayCompatibility}% - Có thể phù hợp</>\n                              )}\n                            </small>\n                          </div>\n                        </div>\n                      </Card.Body>\n                    </Card>\n                  </Col>\n                );\n              })}\n            </Row>\n          </div>\n        )}\n      </Modal.Body>\n    </Modal>\n  );\n};\n\nexport default AISearch;\n\n\n\n\n\n\n\n\n\n\n\n", {"ruleId": "427", "severity": 1, "message": "428", "line": 35, "column": 49, "nodeType": "429", "messageId": "430", "endLine": 35, "endColumn": 51}, {"ruleId": "427", "severity": 1, "message": "428", "line": 97, "column": 27, "nodeType": "429", "messageId": "430", "endLine": 97, "endColumn": 29}, {"ruleId": "431", "severity": 1, "message": "432", "line": 88, "column": 15, "nodeType": "433", "messageId": "434", "endLine": 88, "endColumn": 19}, {"ruleId": "435", "severity": 1, "message": "436", "line": 136, "column": 6, "nodeType": "437", "endLine": 136, "endColumn": 8, "suggestions": "438"}, {"ruleId": "435", "severity": 1, "message": "439", "line": 159, "column": 6, "nodeType": "437", "endLine": 159, "endColumn": 28, "suggestions": "440"}, {"ruleId": "435", "severity": 1, "message": "441", "line": 200, "column": 6, "nodeType": "437", "endLine": 200, "endColumn": 18, "suggestions": "442"}, {"ruleId": "431", "severity": 1, "message": "443", "line": 3, "column": 16, "nodeType": "433", "messageId": "434", "endLine": 3, "endColumn": 22}, {"ruleId": "431", "severity": 1, "message": "444", "line": 3, "column": 24, "nodeType": "433", "messageId": "434", "endLine": 3, "endColumn": 27}, {"ruleId": "431", "severity": 1, "message": "445", "line": 3, "column": 29, "nodeType": "433", "messageId": "434", "endLine": 3, "endColumn": 32}, {"ruleId": "431", "severity": 1, "message": "446", "line": 2, "column": 10, "nodeType": "433", "messageId": "434", "endLine": 2, "endColumn": 19}, {"ruleId": "431", "severity": 1, "message": "444", "line": 2, "column": 21, "nodeType": "433", "messageId": "434", "endLine": 2, "endColumn": 24}, {"ruleId": "431", "severity": 1, "message": "445", "line": 2, "column": 26, "nodeType": "433", "messageId": "434", "endLine": 2, "endColumn": 29}, {"ruleId": "447", "severity": 1, "message": "448", "line": 14, "column": 11, "nodeType": "449", "endLine": 14, "endColumn": 50}, {"ruleId": "447", "severity": 1, "message": "448", "line": 17, "column": 11, "nodeType": "449", "endLine": 17, "endColumn": 50}, {"ruleId": "447", "severity": 1, "message": "448", "line": 20, "column": 11, "nodeType": "449", "endLine": 20, "endColumn": 50}, {"ruleId": "447", "severity": 1, "message": "448", "line": 23, "column": 11, "nodeType": "449", "endLine": 23, "endColumn": 50}, {"ruleId": "447", "severity": 1, "message": "448", "line": 26, "column": 11, "nodeType": "449", "endLine": 26, "endColumn": 50}, {"ruleId": "447", "severity": 1, "message": "448", "line": 29, "column": 11, "nodeType": "449", "endLine": 29, "endColumn": 50}, {"ruleId": "431", "severity": 1, "message": "450", "line": 1, "column": 40, "nodeType": "433", "messageId": "434", "endLine": 1, "endColumn": 48}, {"ruleId": "431", "severity": 1, "message": "451", "line": 4, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 4, "endColumn": 15}, {"ruleId": "431", "severity": 1, "message": "452", "line": 38, "column": 9, "nodeType": "433", "messageId": "434", "endLine": 38, "endColumn": 27}, {"ruleId": "435", "severity": 1, "message": "453", "line": 122, "column": 6, "nodeType": "437", "endLine": 122, "endColumn": 21, "suggestions": "454"}, {"ruleId": "435", "severity": 1, "message": "455", "line": 133, "column": 6, "nodeType": "437", "endLine": 133, "endColumn": 20, "suggestions": "456"}, {"ruleId": "431", "severity": 1, "message": "457", "line": 9, "column": 3, "nodeType": "433", "messageId": "434", "endLine": 9, "endColumn": 7}, {"ruleId": "431", "severity": 1, "message": "458", "line": 10, "column": 3, "nodeType": "433", "messageId": "434", "endLine": 10, "endColumn": 6}, {"ruleId": "431", "severity": 1, "message": "459", "line": 13, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 13, "endColumn": 15}, {"ruleId": "431", "severity": 1, "message": "460", "line": 26, "column": 14, "nodeType": "433", "messageId": "434", "endLine": 26, "endColumn": 30}, {"ruleId": "431", "severity": 1, "message": "461", "line": 57, "column": 10, "nodeType": "433", "messageId": "434", "endLine": 57, "endColumn": 16}, {"ruleId": "435", "severity": 1, "message": "462", "line": 70, "column": 6, "nodeType": "437", "endLine": 70, "endColumn": 8, "suggestions": "463"}, {"ruleId": "431", "severity": 1, "message": "464", "line": 276, "column": 9, "nodeType": "433", "messageId": "434", "endLine": 276, "endColumn": 29}, {"ruleId": "435", "severity": 1, "message": "465", "line": 15, "column": 6, "nodeType": "437", "endLine": 15, "endColumn": 8, "suggestions": "466"}, {"ruleId": "431", "severity": 1, "message": "467", "line": 19, "column": 10, "nodeType": "433", "messageId": "434", "endLine": 19, "endColumn": 23}, {"ruleId": "431", "severity": 1, "message": "468", "line": 23, "column": 24, "nodeType": "433", "messageId": "434", "endLine": 23, "endColumn": 39}, {"ruleId": "427", "severity": 1, "message": "428", "line": 29, "column": 13, "nodeType": "429", "messageId": "430", "endLine": 29, "endColumn": 15}, {"ruleId": "469", "severity": 1, "message": "470", "line": 62, "column": 1, "nodeType": "471", "endLine": 69, "endColumn": 3}, {"ruleId": "431", "severity": 1, "message": "472", "line": 40, "column": 17, "nodeType": "433", "messageId": "434", "endLine": 40, "endColumn": 26}, {"ruleId": "431", "severity": 1, "message": "473", "line": 2, "column": 24, "nodeType": "433", "messageId": "434", "endLine": 2, "endColumn": 33}, {"ruleId": "431", "severity": 1, "message": "444", "line": 2, "column": 35, "nodeType": "433", "messageId": "434", "endLine": 2, "endColumn": 38}, {"ruleId": "431", "severity": 1, "message": "445", "line": 2, "column": 40, "nodeType": "433", "messageId": "434", "endLine": 2, "endColumn": 43}, {"ruleId": "427", "severity": 1, "message": "428", "line": 22, "column": 47, "nodeType": "429", "messageId": "430", "endLine": 22, "endColumn": 49}, {"ruleId": "431", "severity": 1, "message": "472", "line": 1, "column": 17, "nodeType": "433", "messageId": "434", "endLine": 1, "endColumn": 26}, {"ruleId": "431", "severity": 1, "message": "474", "line": 8, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 8, "endColumn": 19}, {"ruleId": "431", "severity": 1, "message": "475", "line": 14, "column": 10, "nodeType": "433", "messageId": "434", "endLine": 14, "endColumn": 15}, {"ruleId": "431", "severity": 1, "message": "476", "line": 9, "column": 10, "nodeType": "433", "messageId": "434", "endLine": 9, "endColumn": 18}, {"ruleId": "431", "severity": 1, "message": "477", "line": 1, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 1, "endColumn": 13}, {"ruleId": "435", "severity": 1, "message": "478", "line": 181, "column": 6, "nodeType": "437", "endLine": 181, "endColumn": 41, "suggestions": "479"}, {"ruleId": "431", "severity": 1, "message": "480", "line": 7, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 7, "endColumn": 21}, {"ruleId": "431", "severity": 1, "message": "481", "line": 9, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 9, "endColumn": 25}, {"ruleId": "431", "severity": 1, "message": "482", "line": 11, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 11, "endColumn": 19}, {"ruleId": "431", "severity": 1, "message": "483", "line": 12, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 12, "endColumn": 24}, {"ruleId": "431", "severity": 1, "message": "484", "line": 19, "column": 11, "nodeType": "433", "messageId": "434", "endLine": 19, "endColumn": 17}, {"ruleId": "431", "severity": 1, "message": "485", "line": 2, "column": 65, "nodeType": "433", "messageId": "434", "endLine": 2, "endColumn": 70}, {"ruleId": "431", "severity": 1, "message": "472", "line": 1, "column": 29, "nodeType": "433", "messageId": "434", "endLine": 1, "endColumn": 38}, {"ruleId": "431", "severity": 1, "message": "474", "line": 4, "column": 8, "nodeType": "433", "messageId": "434", "endLine": 4, "endColumn": 19}, {"ruleId": "431", "severity": 1, "message": "486", "line": 2, "column": 47, "nodeType": "433", "messageId": "434", "endLine": 2, "endColumn": 52}, {"ruleId": "431", "severity": 1, "message": "487", "line": 111, "column": 9, "nodeType": "433", "messageId": "434", "endLine": 111, "endColumn": 29}, {"ruleId": "431", "severity": 1, "message": "488", "line": 118, "column": 9, "nodeType": "433", "messageId": "434", "endLine": 118, "endColumn": 29}, "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["489"], "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", ["490"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["491"], "'Button' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "'Container' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'useState' is defined but never used.", "'Product' is defined but never used.", "'discountedProducts' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getAvailableSizesForColor' and 'selectedSize'. Either include them or remove the dependency array.", ["492"], "React Hook useEffect has missing dependencies: 'getAvailableColorsForSize' and 'selectedColor'. Either include them or remove the dependency array.", ["493"], "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Message' is defined but never used.", "'favoritesLoading' is assigned a value but never used.", "'avatar' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["494"], "'handlePasswordChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["495"], "'payboxMessage' is assigned a value but never used.", "'setSearchParams' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'useEffect' is defined but never used.", "'ListGroup' is defined but never used.", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "'products' is assigned a value but never used.", "'React' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchTransactions' and 'fetchWallet'. Either include them or remove the dependency array.", ["496"], "'PayboxDeposit' is defined but never used.", "'PayboxDepositTest' is defined but never used.", "'PayboxDebug' is defined but never used.", "'PayboxSimpleTest' is defined but never used.", "'wallet' is assigned a value but never used.", "'Alert' is defined but never used.", "'Badge' is defined but never used.", "'getCompatibilityText' is assigned a value but never used.", "'getCompatibilityIcon' is assigned a value but never used.", {"desc": "497", "fix": "498"}, {"desc": "499", "fix": "500"}, {"desc": "497", "fix": "501"}, {"desc": "502", "fix": "503"}, {"desc": "504", "fix": "505"}, {"desc": "506", "fix": "507"}, {"desc": "508", "fix": "509"}, {"desc": "510", "fix": "511"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "512", "text": "513"}, "Update the dependencies array to be: [authTokens, fetchUserProfile, userInfo]", {"range": "514", "text": "515"}, {"range": "516", "text": "513"}, "Update the dependencies array to be: [getAvailableSizesForColor, selectedColor, selectedSize]", {"range": "517", "text": "518"}, "Update the dependencies array to be: [getAvailableColorsForSize, selectedColor, selectedSize]", {"range": "519", "text": "520"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "521", "text": "522"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "523", "text": "524"}, "Update the dependencies array to be: [userInfo, authTokens, userLoading, fetchWallet, fetchTransactions]", {"range": "525", "text": "526"}, [4569, 4571], "[authTokens, refresh]", [5313, 5335], "[authTokens, fetchUserProfile, userInfo]", [6783, 6795], [4149, 4164], "[getAvailableSizesForColor, selectedColor, selectedSize]", [4608, 4622], "[getAvailableColorsForSize, selectedColor, selectedSize]", [2546, 2548], "[navigate, userInfo]", [491, 493], "[logout, navigate, userInfo]", [5731, 5766], "[userInfo, authTokens, userLoading, fetchWallet, fetchTransactions]"]