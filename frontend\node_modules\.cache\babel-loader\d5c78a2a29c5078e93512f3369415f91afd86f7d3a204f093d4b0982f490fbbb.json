{"ast": null, "code": "import axios from \"axios\";\n\n// Set base URL based on environment\nconst getBaseURL = () => {\n  // Check for custom API URL first\n  if (process.env.REACT_APP_API_URL) {\n    return process.env.REACT_APP_API_URL;\n  }\n\n  // In production (Render), use current domain\n  if (process.env.NODE_ENV === 'production') {\n    return window.location.origin;\n  }\n\n  // In development, use localhost\n  return 'http://localhost:8000';\n};\naxios.defaults.baseURL = getBaseURL();\nconsole.log('API Base URL:', getBaseURL());\n\n// Set default headers for JSON\naxios.defaults.headers.common['Content-Type'] = 'application/json';\naxios.defaults.headers.put['Content-Type'] = 'application/json';\naxios.defaults.headers.post['Content-Type'] = 'application/json';\naxios.defaults.headers.patch['Content-Type'] = 'application/json';\nfunction setJwt(jwt) {\n  if (jwt == undefined) {\n    delete axios.defaults.headers.common[\"Authorization\"];\n    return;\n  }\n  axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n  console.log('JWT token set:', `JWT ${jwt.substring(0, 20)}...`);\n}\n\n// Add request interceptor to log requests\naxios.interceptors.request.use(config => {\n  var _config$method;\n  console.log('Making request:', (_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase(), config.url);\n  console.log('Headers:', config.headers);\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add response interceptor to log responses\naxios.interceptors.response.use(response => {\n  console.log('Response received:', response.status, response.config.url);\n  return response;\n}, error => {\n  var _error$response, _error$config, _error$response2;\n  console.log('Request failed:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status, (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url);\n  console.log('Error details:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n  return Promise.reject(error);\n});\nexport default {\n  get: axios.get,\n  post: axios.post,\n  put: axios.put,\n  patch: axios.patch,\n  delete: axios.delete,\n  setJwt\n};", "map": {"version": 3, "names": ["axios", "getBaseURL", "process", "env", "REACT_APP_API_URL", "NODE_ENV", "window", "location", "origin", "defaults", "baseURL", "console", "log", "headers", "common", "put", "post", "patch", "setJwt", "jwt", "undefined", "substring", "interceptors", "request", "use", "config", "_config$method", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "_error$response", "_error$config", "_error$response2", "data", "get", "delete"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/services/httpService.js"], "sourcesContent": ["import axios from \"axios\";\n\n// Set base URL based on environment\nconst getBaseURL = () => {\n    // Check for custom API URL first\n    if (process.env.REACT_APP_API_URL) {\n        return process.env.REACT_APP_API_URL;\n    }\n\n    // In production (Render), use current domain\n    if (process.env.NODE_ENV === 'production') {\n        return window.location.origin;\n    }\n\n    // In development, use localhost\n    return 'http://localhost:8000';\n};\n\naxios.defaults.baseURL = getBaseURL();\nconsole.log('API Base URL:', getBaseURL());\n\n// Set default headers for JSON\naxios.defaults.headers.common['Content-Type'] = 'application/json';\naxios.defaults.headers.put['Content-Type'] = 'application/json';\naxios.defaults.headers.post['Content-Type'] = 'application/json';\naxios.defaults.headers.patch['Content-Type'] = 'application/json';\n\nfunction setJwt(jwt) {\n    if (jwt == undefined) {\n        delete axios.defaults.headers.common[\"Authorization\"];\n        return;\n    }\n    axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n    console.log('JWT token set:', `JWT ${jwt.substring(0, 20)}...`);\n}\n\n// Add request interceptor to log requests\naxios.interceptors.request.use(\n    (config) => {\n        console.log('Making request:', config.method?.toUpperCase(), config.url);\n        console.log('Headers:', config.headers);\n        return config;\n    },\n    (error) => {\n        return Promise.reject(error);\n    }\n);\n\n// Add response interceptor to log responses\naxios.interceptors.response.use(\n    (response) => {\n        console.log('Response received:', response.status, response.config.url);\n        return response;\n    },\n    (error) => {\n        console.log('Request failed:', error.response?.status, error.config?.url);\n        console.log('Error details:', error.response?.data);\n        return Promise.reject(error);\n    }\n);\n\nexport default {\n    get:axios.get,\n    post:axios.post,\n    put:axios.put,\n    patch:axios.patch,\n    delete:axios.delete,\n    setJwt\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACrB;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE;IAC/B,OAAOF,OAAO,CAACC,GAAG,CAACC,iBAAiB;EACxC;;EAEA;EACA,IAAIF,OAAO,CAACC,GAAG,CAACE,QAAQ,KAAK,YAAY,EAAE;IACvC,OAAOC,MAAM,CAACC,QAAQ,CAACC,MAAM;EACjC;;EAEA;EACA,OAAO,uBAAuB;AAClC,CAAC;AAEDR,KAAK,CAACS,QAAQ,CAACC,OAAO,GAAGT,UAAU,EAAE;AACrCU,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEX,UAAU,EAAE,CAAC;;AAE1C;AACAD,KAAK,CAACS,QAAQ,CAACI,OAAO,CAACC,MAAM,CAAC,cAAc,CAAC,GAAG,kBAAkB;AAClEd,KAAK,CAACS,QAAQ,CAACI,OAAO,CAACE,GAAG,CAAC,cAAc,CAAC,GAAG,kBAAkB;AAC/Df,KAAK,CAACS,QAAQ,CAACI,OAAO,CAACG,IAAI,CAAC,cAAc,CAAC,GAAG,kBAAkB;AAChEhB,KAAK,CAACS,QAAQ,CAACI,OAAO,CAACI,KAAK,CAAC,cAAc,CAAC,GAAG,kBAAkB;AAEjE,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,IAAIA,GAAG,IAAIC,SAAS,EAAE;IAClB,OAAOpB,KAAK,CAACS,QAAQ,CAACI,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrD;EACJ;EACAd,KAAK,CAACS,QAAQ,CAACI,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAI,OAAMK,GAAI,EAAC;EAC7DR,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAG,OAAMO,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAE,KAAI,CAAC;AACnE;;AAEA;AACArB,KAAK,CAACsB,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACRf,OAAO,CAACC,GAAG,CAAC,iBAAiB,GAAAc,cAAA,GAAED,MAAM,CAACE,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,WAAW,EAAE,EAAEH,MAAM,CAACI,GAAG,CAAC;EACxElB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEa,MAAM,CAACZ,OAAO,CAAC;EACvC,OAAOY,MAAM;AACjB,CAAC,EACAK,KAAK,IAAK;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CACJ;;AAED;AACA9B,KAAK,CAACsB,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACVtB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqB,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAACR,MAAM,CAACI,GAAG,CAAC;EACvE,OAAOI,QAAQ;AACnB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,eAAA,EAAAC,aAAA,EAAAC,gBAAA;EACP1B,OAAO,CAACC,GAAG,CAAC,iBAAiB,GAAAuB,eAAA,GAAEL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBD,MAAM,GAAAE,aAAA,GAAEN,KAAK,CAACL,MAAM,cAAAW,aAAA,uBAAZA,aAAA,CAAcP,GAAG,CAAC;EACzElB,OAAO,CAACC,GAAG,CAAC,gBAAgB,GAAAyB,gBAAA,GAAEP,KAAK,CAACG,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBC,IAAI,CAAC;EACnD,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CACJ;AAED,eAAe;EACXS,GAAG,EAACvC,KAAK,CAACuC,GAAG;EACbvB,IAAI,EAAChB,KAAK,CAACgB,IAAI;EACfD,GAAG,EAACf,KAAK,CAACe,GAAG;EACbE,KAAK,EAACjB,KAAK,CAACiB,KAAK;EACjBuB,MAAM,EAACxC,KAAK,CAACwC,MAAM;EACnBtB;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}