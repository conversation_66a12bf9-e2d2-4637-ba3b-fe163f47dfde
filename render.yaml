services:
  # Web Service (Django Backend + React Frontend)
  - type: web
    name: ecommerce-web
    env: python
    buildCommand: "./build.sh"
    startCommand: "gunicorn backend.wsgi:application --bind 0.0.0.0:$PORT"
    plan: starter
    healthCheckPath: /api/health/
    pythonVersion: "3.10.13"
    envVars:
      - key: NODE_VERSION
        value: 18.17.0
      - key: DJANGO_SETTINGS_MODULE
        value: backend.settings_render
      - key: DEBUG
        value: False
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: ecommerce-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: ecommerce-redis
          property: connectionString
      - key: STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: EMAIL_HOST_USER
        sync: false
      - key: EMAIL_HOST_PASSWORD
        sync: false
      - key: DEFAULT_FROM_EMAIL
        sync: false

  # Background Worker (for Channels/WebSocket)
  - type: worker
    name: ecommerce-worker
    env: python
    buildCommand: "./build.sh"
    startCommand: "python manage.py runworker"
    plan: starter
    pythonVersion: "3.10.13"
    envVars:
      - key: DJANGO_SETTINGS_MODULE
        value: backend.settings_render
      - key: DEBUG
        value: False
      - key: SECRET_KEY
        fromService:
          type: web
          name: ecommerce-web
          envVarKey: SECRET_KEY
      - key: DATABASE_URL
        fromDatabase:
          name: ecommerce-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: ecommerce-redis
          property: connectionString

databases:
  - name: ecommerce-db
    databaseName: ecommerce_prod
    user: ecommerce_user
    plan: starter

services:
  - type: redis
    name: ecommerce-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru
